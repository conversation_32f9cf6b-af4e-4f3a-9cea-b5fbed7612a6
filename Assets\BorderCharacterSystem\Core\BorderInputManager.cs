using UnityEngine;
using UnityEngine.Events;
using UnityEngine.InputSystem;

namespace BorderCharacterSystem.Core
{
    /// <summary>
    /// Quản lý input cho Border Character System
    /// Wrapper cho Unity Input System với interface tiếng Việt
    /// </summary>
    [AddComponentMenu("Border Character System/Border Input Manager")]
    public class BorderInputManager : MonoBehaviour
    {
        #region Input Actions

        [Header("Input Actions")]
        [SerializeField]
        [Tooltip("Input Action Asset chứa tất cả input mappings")]
        private InputActionAsset inputActions;

        // Input Action references
        private InputAction moveAction;
        private InputAction lookAction;
        private InputAction jumpAction;
        private InputAction crouchAction;
        private InputAction sprintAction;
        private InputAction interactAction;

        #endregion

        #region Input Values

        [Header("Input Values - Chỉ Đọc")]
        [SerializeField]
        [Tooltip("Giá trị input di chuyển hiện tại")]
        private Vector2 giaTriDiChuyen;

        [SerializeField]
        [Tooltip("Giá trị input nhìn hiện tại")]
        private Vector2 giaTriNhin;

        [SerializeField]
        [Tooltip("Trạng thái nút nhảy")]
        private bool dangNhanNutNhay;

        [SerializeField]
        [Tooltip("Trạng thái nút cúi")]
        private bool dangNhanNutCui;

        [SerializeField]
        [Tooltip("Trạng thái nút chạy")]
        private bool dangNhanNutChay;

        [SerializeField]
        [Tooltip("Trạng thái nút tương tác")]
        private bool dangNhanNutTuongTac;

        #endregion

        #region Properties

        /// <summary>Giá trị input di chuyển (WASD/Left Stick)</summary>
        public Vector2 GiaTriDiChuyen => giaTriDiChuyen;

        /// <summary>Giá trị input nhìn (Mouse/Right Stick)</summary>
        public Vector2 GiaTriNhin => giaTriNhin;

        /// <summary>Có đang nhấn nút nhảy không?</summary>
        public bool DangNhanNutNhay => dangNhanNutNhay;

        /// <summary>Có đang nhấn nút cúi không?</summary>
        public bool DangNhanNutCui => dangNhanNutCui;

        /// <summary>Có đang nhấn nút chạy không?</summary>
        public bool DangNhanNutChay => dangNhanNutChay;

        /// <summary>Có đang nhấn nút tương tác không?</summary>
        public bool DangNhanNutTuongTac => dangNhanNutTuongTac;

        /// <summary>Có input di chuyển không?</summary>
        public bool CoInputDiChuyen => giaTriDiChuyen.magnitude > 0.01f;

        /// <summary>Có input nhìn không?</summary>
        public bool CoInputNhin => giaTriNhin.magnitude > 0.01f;

        #endregion

        #region Events

        [Header("Input Events")]
        [Tooltip("Sự kiện khi bắt đầu di chuyển")]
        public UnityEvent OnBatDauDiChuyen = new UnityEvent();

        [Tooltip("Sự kiện khi dừng di chuyển")]
        public UnityEvent OnDungDiChuyen = new UnityEvent();

        [Tooltip("Sự kiện khi nhấn nút nhảy")]
        public UnityEvent OnNhanNutNhay = new UnityEvent();

        [Tooltip("Sự kiện khi thả nút nhảy")]
        public UnityEvent OnThaNutNhay = new UnityEvent();

        [Tooltip("Sự kiện khi nhấn nút cúi")]
        public UnityEvent OnNhanNutCui = new UnityEvent();

        [Tooltip("Sự kiện khi thả nút cúi")]
        public UnityEvent OnThaNutCui = new UnityEvent();

        [Tooltip("Sự kiện khi nhấn nút chạy")]
        public UnityEvent OnNhanNutChay = new UnityEvent();

        [Tooltip("Sự kiện khi thả nút chạy")]
        public UnityEvent OnThaNutChay = new UnityEvent();

        [Tooltip("Sự kiện khi nhấn nút tương tác")]
        public UnityEvent OnNhanNutTuongTac = new UnityEvent();

        #endregion

        #region Private Fields

        private bool wasMoving = false;
        private const string LOG_PREFIX = "[BorderInputManager]";

        #endregion

        #region Unity Lifecycle

        private void Awake()
        {
            InitializeInputActions();
        }

        private void OnEnable()
        {
            EnableInputActions();
        }

        private void OnDisable()
        {
            DisableInputActions();
        }

        private void Update()
        {
            UpdateInputValues();
            CheckMovementEvents();
        }

        #endregion

        #region Initialization

        /// <summary>
        /// Khởi tạo Input Actions
        /// </summary>
        private void InitializeInputActions()
        {
            // Nếu không có Input Action Asset, tạo default
            if (inputActions == null)
            {
                Debug.LogWarning($"{LOG_PREFIX} Không có Input Action Asset. Sử dụng input mặc định.");
                CreateDefaultInputActions();
                return;
            }

            // Lấy references đến các actions
            var playerMap = inputActions.FindActionMap("Player");
            if (playerMap != null)
            {
                moveAction = playerMap.FindAction("Move");
                lookAction = playerMap.FindAction("Look");
                jumpAction = playerMap.FindAction("Jump");
                crouchAction = playerMap.FindAction("Crouch");
                sprintAction = playerMap.FindAction("Sprint");
                interactAction = playerMap.FindAction("Interact");
            }

            Debug.Log($"{LOG_PREFIX} Đã khởi tạo Input Actions");
        }

        /// <summary>
        /// Tạo Input Actions mặc định nếu không có Asset
        /// </summary>
        private void CreateDefaultInputActions()
        {
            // Implementation sẽ được thêm trong Phase 2
            Debug.LogWarning($"{LOG_PREFIX} Chức năng tạo Input Actions mặc định sẽ được implement trong Phase 2");
        }

        #endregion

        #region Input Action Management

        /// <summary>
        /// Bật tất cả Input Actions
        /// </summary>
        private void EnableInputActions()
        {
            if (inputActions != null)
            {
                inputActions.Enable();
                RegisterInputCallbacks();
                Debug.Log($"{LOG_PREFIX} Đã bật Input Actions");
            }
        }

        /// <summary>
        /// Tắt tất cả Input Actions
        /// </summary>
        private void DisableInputActions()
        {
            if (inputActions != null)
            {
                UnregisterInputCallbacks();
                inputActions.Disable();
                Debug.Log($"{LOG_PREFIX} Đã tắt Input Actions");
            }
        }

        /// <summary>
        /// Đăng ký callbacks cho Input Actions
        /// </summary>
        private void RegisterInputCallbacks()
        {
            if (jumpAction != null)
            {
                jumpAction.performed += OnJumpPerformed;
                jumpAction.canceled += OnJumpCanceled;
            }

            if (crouchAction != null)
            {
                crouchAction.performed += OnCrouchPerformed;
                crouchAction.canceled += OnCrouchCanceled;
            }

            if (sprintAction != null)
            {
                sprintAction.performed += OnSprintPerformed;
                sprintAction.canceled += OnSprintCanceled;
            }

            if (interactAction != null)
            {
                interactAction.performed += OnInteractPerformed;
            }
        }

        /// <summary>
        /// Hủy đăng ký callbacks
        /// </summary>
        private void UnregisterInputCallbacks()
        {
            if (jumpAction != null)
            {
                jumpAction.performed -= OnJumpPerformed;
                jumpAction.canceled -= OnJumpCanceled;
            }

            if (crouchAction != null)
            {
                crouchAction.performed -= OnCrouchPerformed;
                crouchAction.canceled -= OnCrouchCanceled;
            }

            if (sprintAction != null)
            {
                sprintAction.performed -= OnSprintPerformed;
                sprintAction.canceled -= OnSprintCanceled;
            }

            if (interactAction != null)
            {
                interactAction.performed -= OnInteractPerformed;
            }
        }

        #endregion

        #region Input Callbacks

        private void OnJumpPerformed(InputAction.CallbackContext context)
        {
            dangNhanNutNhay = true;
            OnNhanNutNhay?.Invoke();
        }

        private void OnJumpCanceled(InputAction.CallbackContext context)
        {
            dangNhanNutNhay = false;
            OnThaNutNhay?.Invoke();
        }

        private void OnCrouchPerformed(InputAction.CallbackContext context)
        {
            dangNhanNutCui = true;
            OnNhanNutCui?.Invoke();
        }

        private void OnCrouchCanceled(InputAction.CallbackContext context)
        {
            dangNhanNutCui = false;
            OnThaNutCui?.Invoke();
        }

        private void OnSprintPerformed(InputAction.CallbackContext context)
        {
            dangNhanNutChay = true;
            OnNhanNutChay?.Invoke();
        }

        private void OnSprintCanceled(InputAction.CallbackContext context)
        {
            dangNhanNutChay = false;
            OnThaNutChay?.Invoke();
        }

        private void OnInteractPerformed(InputAction.CallbackContext context)
        {
            dangNhanNutTuongTac = true;
            OnNhanNutTuongTac?.Invoke();
            
            // Reset interaction state after one frame
            StartCoroutine(ResetInteractionState());
        }

        private System.Collections.IEnumerator ResetInteractionState()
        {
            yield return null;
            dangNhanNutTuongTac = false;
        }

        #endregion

        #region Input Value Updates

        /// <summary>
        /// Cập nhật giá trị input mỗi frame
        /// </summary>
        private void UpdateInputValues()
        {
            // Update movement input
            if (moveAction != null)
            {
                giaTriDiChuyen = moveAction.ReadValue<Vector2>();
            }

            // Update look input
            if (lookAction != null)
            {
                giaTriNhin = lookAction.ReadValue<Vector2>();
            }
        }

        /// <summary>
        /// Kiểm tra và trigger movement events
        /// </summary>
        private void CheckMovementEvents()
        {
            bool isCurrentlyMoving = CoInputDiChuyen;

            if (isCurrentlyMoving && !wasMoving)
            {
                OnBatDauDiChuyen?.Invoke();
            }
            else if (!isCurrentlyMoving && wasMoving)
            {
                OnDungDiChuyen?.Invoke();
            }

            wasMoving = isCurrentlyMoving;
        }

        #endregion

        #region Public API

        /// <summary>
        /// Thiết lập Input Action Asset mới
        /// </summary>
        /// <param name="newInputActions">Input Action Asset mới</param>
        public void ThietLapInputActions(InputActionAsset newInputActions)
        {
            if (newInputActions == null)
            {
                Debug.LogError($"{LOG_PREFIX} Input Action Asset không được để trống!");
                return;
            }

            // Disable current actions
            DisableInputActions();

            // Set new actions
            inputActions = newInputActions;

            // Reinitialize
            InitializeInputActions();
            EnableInputActions();

            Debug.Log($"{LOG_PREFIX} Đã thiết lập Input Action Asset mới");
        }

        /// <summary>
        /// Bật/tắt input
        /// </summary>
        /// <param name="enabled">True để bật, false để tắt</param>
        public void BatTatInput(bool enabled)
        {
            if (enabled)
                EnableInputActions();
            else
                DisableInputActions();
        }

        /// <summary>
        /// Simulate movement input (for testing)
        /// </summary>
        /// <param name="movement">Movement vector</param>
        public void SimulateMovementInput(Vector2 movement)
        {
            giaTriDiChuyen = movement;
        }

        /// <summary>
        /// Simulate jump input (for testing)
        /// </summary>
        public void SimulateJumpInput()
        {
            OnJumpPerformed(new InputAction.CallbackContext());
        }

        /// <summary>
        /// Simulate crouch input (for testing)
        /// </summary>
        /// <param name="crouching">Crouch state</param>
        public void SimulateCrouchInput(bool crouching)
        {
            if (crouching)
                OnCrouchPerformed(new InputAction.CallbackContext());
            else
                OnCrouchCanceled(new InputAction.CallbackContext());
        }

        #endregion
    }
}
