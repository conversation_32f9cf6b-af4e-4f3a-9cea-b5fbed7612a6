using UnityEngine;

namespace BorderCharacterSystem.Configuration.Settings
{
    /// <summary>
    /// Cài đặt liên quan đến camera
    /// </summary>
    [System.Serializable]
    public class CaiDatCamera
    {
        [Header("Độ Nhạy")]
        [Range(0.1f, 10f)]
        [Tooltip("Độ nhạy chuột X")]
        public float doNhayChuotX = 2f;
        
        [Range(0.1f, 10f)]
        [Tooltip("Độ nhạy chuột Y")]
        public float doNhayChuotY = 2f;
        
        [Header("Giới Hạn")]
        [Range(-90f, 0f)]
        [Tooltip("Góc nhìn xuống tối đa")]
        public float gocNhinXuongToiDa = -80f;
        
        [Range(0f, 90f)]
        [Tooltip("Góc nhìn lên tối đa")]
        public float gocNhinLenToiDa = 80f;

        public bool Validate()
        {
            return doNhayChuotX > 0 && doNhayChuotY > 0;
        }

        public CaiDatCamera Clone()
        {
            return new CaiDatCamera
            {
                doNhayChuotX = this.doNhayChuotX,
                doNhayChuotY = this.doNhayChuotY,
                gocNhinXuongToiDa = this.gocNhinXuongToiDa,
                gocNhinLenToiDa = this.gocNhinLenToiDa
            };
        }
    }
}
