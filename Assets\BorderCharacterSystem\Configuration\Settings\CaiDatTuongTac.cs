using UnityEngine;

namespace BorderCharacterSystem.Configuration.Settings
{
    /// <summary>
    /// Cài đặt liên quan đến tương tác
    /// </summary>
    [System.Serializable]
    public class CaiDatTuongTac
    {
        [Header("Khoảng Cách")]
        [Range(0.5f, 10f)]
        [Tooltip("Khoảng cách tương tác tối đa")]
        public float khoangCachTuongTacToiDa = 3f;
        
        [Header("UI")]
        [Tooltip("Hiển thị UI tương tác")]
        public bool hienThiUITuongTac = true;
        
        [Tooltip("Thời gian hiển thị UI")]
        public float thoiGianHienThiUI = 0.5f;

        public bool Validate()
        {
            return khoangCachTuongTacToiDa > 0;
        }

        public CaiDatTuongTac Clone()
        {
            return new CaiDatTuongTac
            {
                khoangCachTuongTacToiDa = this.khoangCachTuongTacToiDa,
                hienThiUITuongTac = this.hienThiUITuongTac,
                thoiGianHienThiUI = this.thoiGianHienThiUI
            };
        }
    }
}
