using UnityEngine;

namespace BorderCharacterSystem.Configuration.Settings
{
    /// <summary>
    /// Cài đặt liên quan đến debug và profiling
    /// </summary>
    [System.Serializable]
    public class CaiDatDebug
    {
        [Header("Debug Mode")]
        [Tooltip("Bật chế độ debug")]
        public bool batCheDoDebug = false;
        
        [Tooltip("Hiển thị thông tin trên màn hình")]
        public bool hienThiThongTinTrenManHinh = false;
        
        [Header("Profiling")]
        [Tooltip("Bật profiling")]
        public bool batProfiling = false;
        
        [Tooltip("Tần suất cập nhật profiling (giây)")]
        public float tanSuatCapNhatProfiling = 1f;

        public bool Validate()
        {
            return tanSuatCapNhatProfiling > 0;
        }

        public CaiDatDebug Clone()
        {
            return new CaiDatDebug
            {
                batCheDoDebug = this.batCheDoDebug,
                hienThiThongTinTrenManHinh = this.hienThiThongTinTrenManHinh,
                batProfiling = this.batProfiling,
                tanSuatCapNhatProfiling = this.tanSuatCapNhatProfiling
            };
        }
    }
}
