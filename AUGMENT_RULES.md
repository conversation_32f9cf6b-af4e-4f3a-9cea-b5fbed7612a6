# Quy Tắc Augment AI - Boder Documentary

Tài liệu này định nghĩa coding standards, conventions và guidelines cho Augment AI khi làm việc với dự án "Boder Documentary".

## 🎯 Mục Tiêu Chính

1. **Tính nhất quán:** Đảm bảo code style và architecture patterns nhất quán
2. **Tích hợp mượt mà:** Tương thích hoàn hảo với Nappin PhysicsCharacterController
3. **Hiệu suất cao:** Duy trì 60 FPS target trên mid-range PC
4. **Modularity:** Component-based architecture cho easy maintenance
5. **Tiếng Việt:** Hỗ trợ đầy đủ Vietnamese language trong game

---

## 📁 Cấu Trúc Dự Án và Naming Conventions

### Folder Structure Rules
```
Assets/BoderDocumentary/
├── Scripts/
│   ├── Core/           # GameManager, SceneManager, SaveSystem
│   ├── Character/      # Character controllers và systems
│   ├── Camera/         # Camera controllers và photo mode
│   ├── Interaction/    # Interaction systems và objects
│   ├── UI/            # User interface components
│   └── Audio/         # Audio management systems
├── Prefabs/           # Game objects prefabs
├── Materials/         # Materials và shaders
├── Scenes/           # Game scenes
└── Resources/        # Runtime loadable assets
```

### Naming Conventions

#### C# Classes và Files
```csharp
// ✅ ĐÚNG - PascalCase cho classes
public class DocumentaryCharacterController : MonoBehaviour
public class InteractableObject : MonoBehaviour
public class PhotoModeSystem : MonoBehaviour

// ✅ ĐÚNG - Interface với prefix I
public interface IInteractable
public interface ISaveable

// ✅ ĐÚNG - Enums với PascalCase
public enum GameState
{
    MainMenu,
    Investigation,
    Interview
}
```

#### Variables và Methods
```csharp
// ✅ ĐÚNG - camelCase cho private fields
private float movementSpeed;
private bool isInvestigating;

// ✅ ĐÚNG - PascalCase cho public properties
public float MovementSpeed { get; set; }
public bool IsInvestigating { get; private set; }

// ✅ ĐÚNG - PascalCase cho methods
public void StartInvestigation()
public bool CanInteract()
```

#### Unity Specific
```csharp
// ✅ ĐÚNG - SerializeField cho inspector exposure
[SerializeField] private float interactionRange = 2f;
[SerializeField] private LayerMask interactableLayer;

// ✅ ĐÚNG - Header attributes cho organization
[Header("Movement Settings")]
[SerializeField] private float walkSpeed = 3f;
[SerializeField] private float runSpeed = 6f;

[Header("Interaction Settings")]
[SerializeField] private float interactionRange = 2f;
```

---

## 🏗️ Architecture Patterns và Best Practices

### Singleton Pattern (Chỉ cho Core Systems)
```csharp
// ✅ ĐÚNG - Thread-safe singleton
public class GameManager : MonoBehaviour
{
    private static GameManager _instance;
    public static GameManager Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = FindObjectOfType<GameManager>();
                if (_instance == null)
                {
                    GameObject go = new GameObject("GameManager");
                    _instance = go.AddComponent<GameManager>();
                    DontDestroyOnLoad(go);
                }
            }
            return _instance;
        }
    }

    private void Awake()
    {
        if (_instance != null && _instance != this)
        {
            Destroy(gameObject);
            return;
        }
        _instance = this;
        DontDestroyOnLoad(gameObject);
    }
}
```

### Event System Pattern
```csharp
// ✅ ĐÚNG - Static events cho global communication
public static class DocumentaryEvents
{
    public static System.Action<ClueData> OnClueDiscovered;
    public static System.Action<float> OnProgressUpdated;
    public static System.Action<GameState> OnGameStateChanged;
    
    // Safe event invocation
    public static void InvokeClueDiscovered(ClueData clue)
    {
        OnClueDiscovered?.Invoke(clue);
    }
}

// ✅ ĐÚNG - Subscribe/Unsubscribe pattern
public class ClueManager : MonoBehaviour
{
    private void OnEnable()
    {
        DocumentaryEvents.OnClueDiscovered += HandleClueDiscovered;
    }
    
    private void OnDisable()
    {
        DocumentaryEvents.OnClueDiscovered -= HandleClueDiscovered;
    }
    
    private void HandleClueDiscovered(ClueData clue)
    {
        // Handle clue discovery
    }
}
```

### Component-Based Architecture
```csharp
// ✅ ĐÚNG - Small, focused components
public class InteractionDetector : MonoBehaviour
{
    [SerializeField] private float detectionRange = 2f;
    [SerializeField] private LayerMask interactableLayer;
    
    public IInteractable GetNearestInteractable()
    {
        // Implementation
    }
}

public class InteractionHandler : MonoBehaviour
{
    private InteractionDetector detector;
    
    private void Awake()
    {
        detector = GetComponent<InteractionDetector>();
    }
}
```

---

## 🔗 Nappin PhysicsCharacterController Integration Rules

### Extension Guidelines
```csharp
// ✅ ĐÚNG - Extend existing systems, don't replace
public class DocumentaryCharacterController : MonoBehaviour
{
    [Header("Nappin Integration")]
    [SerializeField] private CharacterManager characterManager;
    [SerializeField] private InputReader inputReader;
    
    [Header("Documentary Features")]
    [SerializeField] private float investigationSpeed = 1.5f;
    [SerializeField] private bool canPhotograph = true;
    
    private void Start()
    {
        // Use existing Nappin components
        characterManager = GetComponent<CharacterManager>();
        inputReader = GetComponent<InputReader>();
    }
    
    // Add documentary-specific functionality
    public void EnterInvestigationMode()
    {
        characterManager.SetMovementSpeed(investigationSpeed);
    }
}
```

### Input System Integration
```csharp
// ✅ ĐÚNG - Extend InputReader, don't replace
public class DocumentaryInputReader : InputReader
{
    [Header("Documentary Controls")]
    public InputActionReference photographAction;
    public InputActionReference investigateAction;
    
    protected override void OnEnable()
    {
        base.OnEnable();
        photographAction.action.performed += OnPhotographPerformed;
        investigateAction.action.performed += OnInvestigatePerformed;
    }
    
    protected override void OnDisable()
    {
        base.OnDisable();
        photographAction.action.performed -= OnPhotographPerformed;
        investigateAction.action.performed -= OnInvestigatePerformed;
    }
}
```

---

## 🎮 Unity Development Guidelines

### Performance Rules
```csharp
// ✅ ĐÚNG - Cache components
public class InteractableObject : MonoBehaviour
{
    private Renderer objectRenderer;
    private Collider objectCollider;
    
    private void Awake()
    {
        objectRenderer = GetComponent<Renderer>();
        objectCollider = GetComponent<Collider>();
    }
    
    // ❌ TRÁNH - GetComponent trong Update
    private void Update()
    {
        // ❌ BAD: GetComponent<Renderer>().material.color = Color.red;
        // ✅ GOOD: objectRenderer.material.color = Color.red;
    }
}

// ✅ ĐÚNG - Object pooling cho frequent instantiation
public class ObjectPool<T> where T : MonoBehaviour
{
    private Queue<T> pool = new Queue<T>();
    private T prefab;
    
    public T Get()
    {
        if (pool.Count > 0)
            return pool.Dequeue();
        else
            return Object.Instantiate(prefab);
    }
    
    public void Return(T obj)
    {
        obj.gameObject.SetActive(false);
        pool.Enqueue(obj);
    }
}
```

### Memory Management
```csharp
// ✅ ĐÚNG - Proper cleanup
public class AudioManager : MonoBehaviour
{
    private List<AudioSource> audioSources = new List<AudioSource>();
    
    private void OnDestroy()
    {
        // Clean up resources
        foreach (var source in audioSources)
        {
            if (source != null)
                source.Stop();
        }
        audioSources.Clear();
    }
}

// ✅ ĐÚNG - Avoid string concatenation trong runtime
public class UIManager : MonoBehaviour
{
    private StringBuilder stringBuilder = new StringBuilder();
    
    public void UpdateScoreText(int score)
    {
        stringBuilder.Clear();
        stringBuilder.Append("Score: ");
        stringBuilder.Append(score);
        scoreText.text = stringBuilder.ToString();
    }
}
```

---

## 🌐 Vietnamese Language Support

### Text Management
```csharp
// ✅ ĐÚNG - Localization system
[CreateAssetMenu(fileName = "LocalizedText", menuName = "Boder/Localized Text")]
public class LocalizedText : ScriptableObject
{
    [System.Serializable]
    public class LocalizedString
    {
        public string key;
        [TextArea(3, 5)]
        public string vietnamese;
        [TextArea(3, 5)]
        public string english;
    }
    
    public List<LocalizedString> strings = new List<LocalizedString>();
    
    public string GetText(string key, SystemLanguage language = SystemLanguage.Vietnamese)
    {
        var entry = strings.Find(s => s.key == key);
        if (entry != null)
        {
            return language == SystemLanguage.Vietnamese ? entry.vietnamese : entry.english;
        }
        return $"[MISSING: {key}]";
    }
}
```

### Font và UI Guidelines
```csharp
// ✅ ĐÚNG - Vietnamese font support
public class UITextManager : MonoBehaviour
{
    [Header("Vietnamese Font Support")]
    [SerializeField] private TMP_FontAsset vietnameseFont;
    [SerializeField] private TMP_FontAsset englishFont;
    
    public void SetLanguage(SystemLanguage language)
    {
        var textComponents = FindObjectsOfType<TextMeshProUGUI>();
        var targetFont = language == SystemLanguage.Vietnamese ? vietnameseFont : englishFont;
        
        foreach (var text in textComponents)
        {
            text.font = targetFont;
        }
    }
}
```

---

## 📝 Code Documentation Standards

### XML Documentation
```csharp
/// <summary>
/// Quản lý hệ thống tương tác trong documentary gameplay
/// Manages interaction system for documentary gameplay
/// </summary>
public class InteractionManager : MonoBehaviour
{
    /// <summary>
    /// Kiểm tra xem object có thể tương tác được không
    /// Checks if an object can be interacted with
    /// </summary>
    /// <param name="target">Object cần kiểm tra / Object to check</param>
    /// <returns>True nếu có thể tương tác / True if interaction is possible</returns>
    public bool CanInteract(GameObject target)
    {
        return target.GetComponent<IInteractable>() != null;
    }
}
```

### Comment Guidelines
```csharp
public class DocumentaryCharacterController : MonoBehaviour
{
    // TODO: Implement photo mode transition
    // FIXME: Character sometimes gets stuck on slopes
    // NOTE: This integrates with Nappin PhysicsCharacterController
    
    /// <summary>
    /// Chuyển sang chế độ chụp ảnh
    /// Switches to photo mode
    /// </summary>
    public void EnterPhotoMode()
    {
        // Disable character movement
        characterManager.SetMovementEnabled(false);
        
        // Enable photo mode camera
        photoModeCamera.gameObject.SetActive(true);
        
        // Show photo mode UI
        photoModeUI.Show();
    }
}
```

---

## 🧪 Testing Guidelines

### Unit Test Structure
```csharp
[TestFixture]
public class InteractionSystemTests
{
    private InteractionManager interactionManager;
    private GameObject testObject;
    
    [SetUp]
    public void Setup()
    {
        var go = new GameObject("InteractionManager");
        interactionManager = go.AddComponent<InteractionManager>();
        
        testObject = new GameObject("TestInteractable");
        testObject.AddComponent<TestInteractable>();
    }
    
    [Test]
    public void CanInteract_WithInteractableObject_ReturnsTrue()
    {
        // Arrange
        var interactable = testObject.GetComponent<IInteractable>();
        
        // Act
        bool result = interactionManager.CanInteract(testObject);
        
        // Assert
        Assert.IsTrue(result);
    }
    
    [TearDown]
    public void TearDown()
    {
        Object.DestroyImmediate(interactionManager.gameObject);
        Object.DestroyImmediate(testObject);
    }
}
```

---

## ⚠️ Common Pitfalls và Solutions

### Performance Pitfalls
```csharp
// ❌ TRÁNH - Expensive operations trong Update
private void Update()
{
    // ❌ BAD: Find operations
    var player = GameObject.FindWithTag("Player");
    
    // ❌ BAD: String operations
    text.text = "Score: " + score.ToString();
    
    // ❌ BAD: Instantiate/Destroy frequently
    Instantiate(bulletPrefab);
}

// ✅ ĐÚNG - Optimized approach
private void Start()
{
    player = GameObject.FindWithTag("Player"); // Cache reference
    stringBuilder = new StringBuilder(); // Reuse StringBuilder
    bulletPool = new ObjectPool<Bullet>(bulletPrefab); // Use object pooling
}
```

### Integration Pitfalls
```csharp
// ❌ TRÁNH - Replacing Nappin components
public class BadCharacterController : MonoBehaviour
{
    // Don't replace existing functionality
}

// ✅ ĐÚNG - Extending Nappin components
public class GoodCharacterController : MonoBehaviour
{
    private CharacterManager nappinCharacterManager;
    
    private void Start()
    {
        nappinCharacterManager = GetComponent<CharacterManager>();
    }
    
    // Add documentary-specific features
    public void AddDocumentaryFeature()
    {
        // Use existing Nappin functionality as base
    }
}
```

---

## 🔄 Code Review Checklist

### Pre-Commit Checklist
- [ ] Code follows naming conventions
- [ ] No hardcoded strings (use localization)
- [ ] Performance considerations addressed
- [ ] Proper error handling implemented
- [ ] XML documentation added
- [ ] Unit tests written (if applicable)
- [ ] Integration với Nappin systems verified
- [ ] Vietnamese language support tested

### Review Criteria
1. **Functionality:** Code works as intended
2. **Performance:** No obvious performance issues
3. **Maintainability:** Code is readable và well-structured
4. **Integration:** Properly integrates với existing systems
5. **Standards:** Follows project conventions
6. **Documentation:** Adequate comments và documentation
