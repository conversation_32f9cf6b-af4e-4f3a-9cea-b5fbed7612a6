using UnityEngine;

namespace BorderCharacterSystem.Configuration.Settings
{
    /// <summary>
    /// Cài đặt liên quan đến localization
    /// </summary>
    [System.Serializable]
    public class CaiDatLocalization
    {
        [Header("Ngôn Ng<PERSON>")]
        [<PERSON>lt<PERSON>("Ngôn ngữ mặc định")]
        public string ngonNguMacDinh = "vi-VN";
        
        [<PERSON>lt<PERSON>("Ngôn ngữ dự phòng")]
        public string ngonNguDuPhong = "en-US";
        
        [Header("Cài Đặt")]
        [Tooltip("Tự động phát hiện ngôn ngữ hệ thống")]
        public bool tuDongPhatHienNgonNgu = true;

        public bool Validate()
        {
            return !string.IsNullOrEmpty(ngonNguMacDinh);
        }

        public CaiDatLocalization Clone()
        {
            return new CaiDatLocalization
            {
                ngonNguMacDinh = this.ngonNguMacDinh,
                ngonNguDuPhong = this.ngonNguDuPhong,
                tuDongPhatHienNgonNgu = this.tuDongPhatHienNgonNgu
            };
        }
    }
}
