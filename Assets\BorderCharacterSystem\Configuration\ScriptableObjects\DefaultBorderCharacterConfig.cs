using UnityEngine;
using BorderCharacterSystem.Configuration;
using BorderCharacterSystem.Configuration.Settings;

namespace BorderCharacterSystem.Configuration.ScriptableObjects
{
    /// <summary>
    /// Factory class để tạo default configuration cho Border Character System
    /// </summary>
    public static class DefaultBorderCharacterConfig
    {
        /// <summary>
        /// Tạo cấu hình mặc định cho game thông thường
        /// </summary>
        /// <returns>BorderCharacterConfig với giá trị mặc định</returns>
        public static BorderCharacterConfig CreateDefault()
        {
            var config = ScriptableObject.CreateInstance<BorderCharacterConfig>();
            
            // Set metadata
            SetDefaultMetadata(config);
            
            // Set default settings
            config.movementSettings = CreateDefaultMovementSettings();
            config.jumpSettings = CreateDefaultJumpSettings();
            config.cameraSettings = CreateDefaultCameraSettings();
            config.interactionSettings = CreateDefaultInteractionSettings();
            config.debugSettings = CreateDefaultDebugSettings();
            config.localizationSettings = CreateDefaultLocalizationSettings();
            
            return config;
        }
        
        /// <summary>
        /// Tạo cấu hình cho documentary game
        /// </summary>
        /// <returns>BorderCharacterConfig tối ưu cho documentary game</returns>
        public static BorderCharacterConfig CreateDocumentaryConfig()
        {
            var config = CreateDefault();
            
            // Adjust for documentary game
            config.movementSettings.tocDoDiBo = 3f; // Chậm hơn để quan sát
            config.movementSettings.tocDoChay = 6f; // Không quá nhanh
            config.movementSettings.thoiGianTangToc = 0.5f; // Mượt hơn
            config.movementSettings.thoiGianGiamToc = 0.3f; // Mượt hơn
            
            config.jumpSettings.lucNhayCoBan = 12f; // Nhảy thấp hơn
            
            config.cameraSettings.doNhayChuotX = 1.5f; // Chậm hơn cho precision
            config.cameraSettings.doNhayChuotY = 1.5f;
            
            config.interactionSettings.khoangCachTuongTacToiDa = 5f; // Xa hơn cho documentary
            config.interactionSettings.hienThiUITuongTac = true;
            
            return config;
        }
        
        /// <summary>
        /// Tạo cấu hình cho action game
        /// </summary>
        /// <returns>BorderCharacterConfig tối ưu cho action game</returns>
        public static BorderCharacterConfig CreateActionConfig()
        {
            var config = CreateDefault();
            
            // Adjust for action game
            config.movementSettings.tocDoDiBo = 8f; // Nhanh hơn
            config.movementSettings.tocDoChay = 15f; // Rất nhanh
            config.movementSettings.thoiGianTangToc = 0.1f; // Responsive
            config.movementSettings.thoiGianGiamToc = 0.05f; // Responsive
            
            config.jumpSettings.lucNhayCoBan = 20f; // Nhảy cao
            config.jumpSettings.heSoNhayTuTuong = 2f; // Wall jump mạnh
            
            config.cameraSettings.doNhayChuotX = 3f; // Nhanh cho action
            config.cameraSettings.doNhayChuotY = 3f;
            
            config.interactionSettings.khoangCachTuongTacToiDa = 2f; // Gần cho precision
            
            return config;
        }
        
        /// <summary>
        /// Tạo cấu hình cho platformer game
        /// </summary>
        /// <returns>BorderCharacterConfig tối ưu cho platformer game</returns>
        public static BorderCharacterConfig CreatePlatformerConfig()
        {
            var config = CreateDefault();
            
            // Adjust for platformer
            config.movementSettings.tocDoDiBo = 6f;
            config.movementSettings.tocDoChay = 12f;
            config.movementSettings.choPhepDiChuyenTrongKhongKhi = true;
            config.movementSettings.heSoDieuKhienKhongKhi = 0.8f; // Điều khiển tốt trong không khí
            
            config.jumpSettings.lucNhayCoBan = 18f;
            config.jumpSettings.heSoNhayTuTuong = 1.8f;
            config.jumpSettings.heSoTrongLucKhiRoi = 1.5f; // Rơi nhanh hơn
            
            config.cameraSettings.doNhayChuotX = 2f;
            config.cameraSettings.doNhayChuotY = 2f;
            
            return config;
        }
        
        #region Private Helper Methods
        
        private static void SetDefaultMetadata(BorderCharacterConfig config)
        {
            // Metadata sẽ được set thông qua reflection hoặc public properties
            // Tạm thời skip vì fields là private
        }
        
        private static CaiDatDiChuyen CreateDefaultMovementSettings()
        {
            return new CaiDatDiChuyen
            {
                // Tốc độ
                tocDoDiBo = 5f,
                tocDoChay = 10f,
                heSoTocDoKhiCui = 0.5f,
                nguongInputToiThieu = 0.01f,
                
                // Gia tốc
                thoiGianTangToc = 0.2f,
                thoiGianGiamToc = 0.1f,
                heSoGiaToc = 1f,
                heSoGiamToc = 1f,
                
                // Ma sát
                maSatMatDat = 0.3f,
                maSatTuong = 0.8f,
                maSatKhongKhi = 0.1f,
                
                // Điều khiển nâng cao
                tocDoXoay = 1f,
                doMuotXoay = 0.1f,
                choPhepDiChuyenTrongKhongKhi = true,
                heSoDieuKhienKhongKhi = 0.5f,
                
                // Slope và terrain
                gocDocToiDa = 45f,
                heSoTocDoLeoDoc = 0.8f,
                heSoTocDoXuongDoc = 1.2f,
                tuDongDieuChinhTocDoTheoDoDoc = true
            };
        }
        
        private static CaiDatNhay CreateDefaultJumpSettings()
        {
            return new CaiDatNhay
            {
                lucNhayCoBan = 15f,
                heSoNhayTuTuong = 1.5f,
                heSoTrongLucKhiRoi = 2f,
                heSoTrongLucKhiGiuNutNhay = 5f
            };
        }
        
        private static CaiDatCamera CreateDefaultCameraSettings()
        {
            return new CaiDatCamera
            {
                doNhayChuotX = 2f,
                doNhayChuotY = 2f,
                gocNhinXuongToiDa = -80f,
                gocNhinLenToiDa = 80f
            };
        }
        
        private static CaiDatTuongTac CreateDefaultInteractionSettings()
        {
            return new CaiDatTuongTac
            {
                khoangCachTuongTacToiDa = 3f,
                hienThiUITuongTac = true,
                thoiGianHienThiUI = 0.5f
            };
        }
        
        private static CaiDatDebug CreateDefaultDebugSettings()
        {
            return new CaiDatDebug
            {
                batCheDoDebug = false,
                hienThiThongTinTrenManHinh = false,
                batProfiling = false,
                tanSuatCapNhatProfiling = 1f
            };
        }
        
        private static CaiDatLocalization CreateDefaultLocalizationSettings()
        {
            return new CaiDatLocalization
            {
                ngonNguMacDinh = "vi-VN",
                ngonNguDuPhong = "en-US",
                tuDongPhatHienNgonNgu = true
            };
        }
        
        #endregion
    }
}
