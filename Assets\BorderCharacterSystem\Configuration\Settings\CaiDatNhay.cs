using UnityEngine;

namespace BorderCharacterSystem.Configuration.Settings
{
    /// <summary>
    /// Cài đặt liên quan đến nhảy và trọng lực
    /// </summary>
    [System.Serializable]
    public class CaiDatNhay
    {
        [Header("Lực Nhảy")]
        [Range(5f, 30f)]
        [Tooltip("Lực nhảy cơ bản")]
        public float lucNhayCoBan = 15f;
        
        [Range(1f, 5f)]
        [Tooltip("Hệ số nhảy từ tường")]
        public float heSoNhayTuTuong = 1.5f;
        
        [Header("Trọng Lực")]
        [Range(1f, 5f)]
        [Tooltip("Hệ số trọng lực khi rơi")]
        public float heSoTrongLucKhiRoi = 2f;
        
        [Range(1f, 10f)]
        [Tooltip("Hệ số trọng lực khi giữ nút nhảy")]
        public float heSoTrongLucKhiGiuNutNhay = 5f;

        public bool Validate()
        {
            return lucNhayCoBan > 0 && heSoNhayTuTuong > 0;
        }

        public CaiDatNhay Clone()
        {
            return new CaiDatNhay
            {
                lucNhayCoBan = this.lucNhayCoBan,
                heSoNhayTuTuong = this.heSoNhayTuTuong,
                heSoTrongLucKhiRoi = this.heSoTrongLucKhiRoi,
                heSoTrongLucKhiGiuNutNhay = this.heSoTrongLucKhiGiuNutNhay
            };
        }
    }
}
