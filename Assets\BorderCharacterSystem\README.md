# Border Character System
## Hệ Thống Character Controller Tùy Chỉnh cho Unity

---

## 🎯 Giới Thiệu

Border Character System là một wrapper/extension system hoàn chỉnh cho Nappin PhysicsCharacterController, đư<PERSON><PERSON> thiết kế đặc biệt cho developers Việt Nam. Hệ thống cung cấp:

- **Interface đơn giản và dễ sử dụng** với tên tiếng Việt
- **Hệ thống cấu hình linh hoạt** với ScriptableObjects
- **Tích hợp tốt với Unity workflow** và best practices
- **Documentation đầy đủ bằng tiếng Việt**
- **Kh<PERSON> năng tái sử dụng** cho nhiều dự án khác nhau

---

## 📋 Yêu Cầu Hệ Thống

### Unity Version
- **Minimum**: Unity 2022.3 LTS
- **Recommended**: Unity 2023.2 LTS hoặc mới hơn

### Dependencies
- **Nappin PhysicsCharacterController** (required)
- **Unity Input System** (required)
- **Cinemachine** (optional, for camera features)
- **TextMeshPro** (optional, for UI)

### Platform Support
- ✅ **Windows** (Primary)
- ✅ **Mac** (Secondary)
- ✅ **Linux** (Secondary)
- 🔄 **Mobile** (Planned for future)

---

## 🚀 Cài Đặt Nhanh

### Bước 1: Import Assets
1. Import Nappin PhysicsCharacterController vào project
2. Import Border Character System package
3. Đảm bảo Unity Input System đã được cài đặt

### Bước 2: Setup Character
1. Tạo GameObject mới cho character
2. Add component `BorderCharacterController`
3. Assign `BorderCharacterConfig` (hoặc để trống để sử dụng config mặc định)
4. Nhấn Play và test!

### Bước 3: Cấu Hình (Tùy Chọn)
1. Tạo `BorderCharacterConfig` asset mới: Right-click → Create → Border Character System → Cấu Hình Nhân Vật
2. Điều chỉnh các thông số theo ý muốn
3. Assign config vào `BorderCharacterController`

---

## 🎮 Sử Dụng Cơ Bản

### Code Example - Cơ Bản
```csharp
using UnityEngine;
using BorderCharacterSystem.Core;

public class VidDuCoBan : MonoBehaviour
{
    public BorderCharacterController nhanVat;
    
    void Start()
    {
        // Đăng ký events
        nhanVat.OnBatDauDiChuyen.AddListener(() => {
            Debug.Log("Nhân vật bắt đầu di chuyển!");
        });
        
        nhanVat.OnBatDauNhay.AddListener(() => {
            Debug.Log("Nhân vật nhảy!");
        });
    }
    
    void Update()
    {
        // Kiểm tra trạng thái
        if (nhanVat.DangDiChuyen)
        {
            Debug.Log($"Tốc độ: {nhanVat.VanToc.magnitude:F2} m/s");
        }
    }
}
```

### Properties Chính
```csharp
// Trạng thái nhân vật
bool DangDungTrenDat    // Có đang đứng trên mặt đất?
bool DangDiChuyen       // Có đang di chuyển?
bool DangNhay           // Có đang nhảy?
bool DangCui            // Có đang cúi?

// Thông tin vật lý
Vector3 VanToc          // Vận tốc hiện tại
Vector3 ViTri           // Vị trí hiện tại
Vector3 HuongNhin       // Hướng nhìn hiện tại
```

### Events Chính
```csharp
OnBatDauDiChuyen        // Khi bắt đầu di chuyển
OnKetThucDiChuyen       // Khi dừng di chuyển
OnBatDauNhay            // Khi bắt đầu nhảy
OnHaCanh                // Khi hạ cánh
OnThayDoiTrangThaiCui   // Khi thay đổi trạng thái cúi
```

---

## ⚙️ Cấu Hình Chi Tiết

### Tạo Configuration Asset
1. Right-click trong Project window
2. Chọn **Create → Border Character System → Cấu Hình Nhân Vật**
3. Đặt tên cho config (ví dụ: "Config_NhanVatChinh")
4. Điều chỉnh các thông số trong Inspector

### Các Nhóm Cài Đặt

#### 🏃 Cài Đặt Di Chuyển
- **Tốc độ đi bộ**: Tốc độ di chuyển bình thường
- **Tốc độ chạy**: Tốc độ khi nhấn nút chạy
- **Hệ số tốc độ khi cúi**: Giảm tốc độ khi cúi
- **Thời gian tăng/giảm tốc**: Độ mượt khi thay đổi tốc độ
- **Ma sát**: Ảnh hưởng đến độ trơn trượt

#### 🦘 Cài Đặt Nhảy
- **Lực nhảy cơ bản**: Độ cao nhảy
- **Hệ số nhảy từ tường**: Nhảy wall jump
- **Trọng lực**: Ảnh hưởng đến cảm giác rơi

#### 📷 Cài Đặt Camera
- **Độ nhạy chuột**: Tốc độ xoay camera
- **Giới hạn góc nhìn**: Góc nhìn lên/xuống tối đa

#### 🤝 Cài Đặt Tương Tác
- **Khoảng cách tương tác**: Phạm vi tương tác với objects
- **UI tương tác**: Hiển thị giao diện tương tác

---

## 🔧 API Reference

### BorderCharacterController

#### Methods Chính
```csharp
// Thiết lập cấu hình mới
void ThietLapCauHinh(BorderCharacterConfig cauHinhMoi)

// Bật/tắt debug mode
void BatTatCheDoDebug(bool batTat)

// Lấy thông tin trạng thái hiện tại
ThongTinTrangThai LayThongTinTrangThai()
```

#### Properties
```csharp
BorderCharacterConfig Config        // Cấu hình hiện tại
CharacterManager NappinController   // Reference đến Nappin controller
BorderInputManager InputManager     // Input manager
BorderEventSystem EventSystem       // Event system
```

### BorderInputManager

#### Properties
```csharp
Vector2 GiaTriDiChuyen     // Input di chuyển (WASD/Left Stick)
Vector2 GiaTriNhin         // Input nhìn (Mouse/Right Stick)
bool DangNhanNutNhay       // Trạng thái nút nhảy
bool DangNhanNutCui        // Trạng thái nút cúi
bool DangNhanNutChay       // Trạng thái nút chạy
```

#### Events
```csharp
OnBatDauDiChuyen          // Khi bắt đầu input di chuyển
OnDungDiChuyen            // Khi dừng input di chuyển
OnNhanNutNhay             // Khi nhấn nút nhảy
OnNhanNutTuongTac         // Khi nhấn nút tương tác
```

---

## 🐛 Troubleshooting

### Lỗi Thường Gặp

#### "CharacterManager component không tìm thấy!"
**Nguyên nhân**: Thiếu Nappin PhysicsCharacterController component
**Giải pháp**: Add component `CharacterManager` vào GameObject

#### "Không có Input Action Asset"
**Nguyên nhân**: Chưa setup Unity Input System
**Giải pháp**: 
1. Window → Package Manager → Install "Input System"
2. Tạo Input Action Asset mới
3. Assign vào BorderInputManager

#### Character không di chuyển
**Kiểm tra**:
1. Có Rigidbody component không?
2. Có Collider component không?
3. Input System có hoạt động không?
4. Config có giá trị hợp lệ không?

#### Performance thấp
**Tối ưu**:
1. Tắt debug mode trong production
2. Giảm tần suất profiling
3. Kiểm tra số lượng events được subscribe

---

## 📚 Tài Liệu Thêm

### Tutorials
- [Bài 1: Setup Cơ Bản](Documentation/Tutorial_01_Setup.md)
- [Bài 2: Cấu Hình Movement](Documentation/Tutorial_02_Movement.md)
- [Bài 3: Tương Tác Với Môi Trường](Documentation/Tutorial_03_Interaction.md)

### Examples
- [Basic Movement Demo](Examples/Scenes/BasicMovementDemo.unity)
- [Platform Demo](Examples/Scenes/PlatformDemo.unity)
- [Documentary Demo](Examples/Scenes/DocumentaryDemo.unity)

### API Documentation
- [Complete API Reference](Documentation/API/README.md)
- [Configuration Guide](Documentation/Configuration/README.md)
- [Event System Guide](Documentation/Events/README.md)

---

## 🤝 Đóng Góp

### Báo Lỗi
Nếu bạn tìm thấy lỗi, vui lòng tạo issue với thông tin:
- Unity version
- Nappin PhysicsCharacterController version
- Border Character System version
- Mô tả chi tiết lỗi
- Steps to reproduce

### Feature Requests
Chúng tôi luôn chào đón ý tưởng mới! Hãy tạo feature request với:
- Mô tả feature
- Use case cụ thể
- Mockup/example nếu có

---

## 📄 License

Border Character System được phát hành dưới MIT License.
Xem [LICENSE](LICENSE.md) để biết thêm chi tiết.

---

## 👥 Credits

- **Nappin PhysicsCharacterController**: Base character controller system
- **Unity Technologies**: Unity Engine và Input System
- **Border Documentary Team**: Development và testing
- **Unity Vietnam Community**: Feedback và support

---

## 📞 Liên Hệ

- **Email**: <EMAIL>
- **Discord**: [Unity Vietnam Community](https://discord.gg/unity-vietnam)
- **GitHub**: [BorderCharacterSystem Repository](https://github.com/BorderDocumentary/BorderCharacterSystem)

---

**Phiên bản**: 1.0.0  
**Ngày cập nhật**: 2025-01-06  
**Tác giả**: Border Character System Team
