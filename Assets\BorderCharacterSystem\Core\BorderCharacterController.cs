using UnityEngine;
using UnityEngine.Events;
using PhysicsCharacterController;
using BorderCharacterSystem.Configuration;
using BorderCharacterSystem.Configuration.ScriptableObjects;

namespace BorderCharacterSystem.Core
{
    /// <summary>
    /// Wrapper ch<PERSON>h cho Nappin PhysicsCharacterController
    /// Cung cấp interface đơn giản và dễ sử dụng cho developers
    /// 
    /// Tác giả: Border Character System Team
    /// Phiên bản: 1.0.0
    /// Ngày tạo: 2025-01-06
    /// </summary>
    [RequireComponent(typeof(CharacterManager))]
    [AddComponentMenu("Border Character System/Border Character Controller")]
    [HelpURL("https://github.com/BorderDocumentary/BorderCharacterSystem/wiki")]
    public class BorderCharacterController : MonoBehaviour
    {
        #region Serialized Fields

        [Header("Cấu Hình Hệ Thống")]
        [SerializeField] 
        [Tooltip("<PERSON><PERSON>u hình chính cho character controller")]
        private BorderCharacterConfig characterConfig;
        
        [SerializeField] 
        [Tooltip("Bật chế độ debug để hiển thị thông tin chi tiết")]
        private bool enableDebugMode = false;
        
        [SerializeField] 
        [Tooltip("Bật profiling để theo dõi hiệu suất")]
        private bool enableProfiling = false;

        [Header("Component References")]
        [SerializeField] 
        [Tooltip("Reference đến CharacterManager của Nappin")]
        private CharacterManager nappinController;
        
        [SerializeField] 
        [Tooltip("Reference đến Input Manager")]
        private BorderInputManager inputManager;
        
        [SerializeField] 
        [Tooltip("Reference đến Event System")]
        private BorderEventSystem eventSystem;

        #endregion

        #region Properties

        /// <summary>Cấu hình hiện tại của character</summary>
        public BorderCharacterConfig Config => characterConfig;
        
        /// <summary>Reference đến Nappin CharacterManager</summary>
        public CharacterManager NappinController => nappinController;
        
        /// <summary>Reference đến Input Manager</summary>
        public BorderInputManager InputManager => inputManager;
        
        /// <summary>Reference đến Event System</summary>
        public BorderEventSystem EventSystem => eventSystem;

        // State Properties với tên tiếng Việt
        /// <summary>Nhân vật có đang đứng trên mặt đất không?</summary>
        public bool DangDungTrenDat => nappinController != null && nappinController.GetGrounded();
        
        /// <summary>Nhân vật có đang di chuyển không?</summary>
        public bool DangDiChuyen => nappinController != null && nappinController.axisInput.magnitude > 0.1f;
        
        /// <summary>Nhân vật có đang nhảy không?</summary>
        public bool DangNhay => nappinController != null && nappinController.GetJumping();
        
        /// <summary>Nhân vật có đang cúi không?</summary>
        public bool DangCui => nappinController != null && nappinController.GetCrouching();
        
        /// <summary>Nhân vật có đang chạm tường không?</summary>
        public bool DangChamTuong => nappinController != null && nappinController.GetTouchingWall();

        // Movement Properties
        /// <summary>Vận tốc hiện tại của nhân vật</summary>
        public Vector3 VanToc => nappinController != null ? nappinController.rigidbody.linearVelocity : Vector3.zero;
        
        /// <summary>Vị trí hiện tại của nhân vật</summary>
        public Vector3 ViTri => transform.position;
        
        /// <summary>Hướng nhìn hiện tại của nhân vật</summary>
        public Vector3 HuongNhin => transform.forward;

        #endregion

        #region Events - Sử dụng tiếng Việt cho user-friendly

        [Header("Sự Kiện Di Chuyển")]
        [Tooltip("Sự kiện khi nhân vật bắt đầu di chuyển")]
        public UnityEvent OnBatDauDiChuyen = new UnityEvent();
        
        [Tooltip("Sự kiện khi nhân vật dừng di chuyển")]
        public UnityEvent OnKetThucDiChuyen = new UnityEvent();
        
        [Tooltip("Sự kiện khi nhân vật bắt đầu nhảy")]
        public UnityEvent OnBatDauNhay = new UnityEvent();
        
        [Tooltip("Sự kiện khi nhân vật hạ cánh")]
        public UnityEvent OnHaCanh = new UnityEvent();
        
        [Tooltip("Sự kiện khi thay đổi trạng thái cúi")]
        public UnityEvent<bool> OnThayDoiTrangThaiCui = new UnityEvent<bool>();

        [Header("Sự Kiện Tương Tác")]
        [Tooltip("Sự kiện khi tương tác với đối tượng")]
        public UnityEvent<GameObject> OnTuongTacVoiDoiTuong = new UnityEvent<GameObject>();
        
        [Tooltip("Sự kiện khi nhận vật phẩm")]
        public UnityEvent<string> OnNhanVatPham = new UnityEvent<string>();

        #endregion

        #region Private Fields

        // State tracking
        private bool wasMoving = false;
        private bool wasGrounded = true;
        private bool wasCrouching = false;
        private bool wasJumping = false;

        // Performance tracking
        private float lastFrameTime = 0f;
        private int frameCount = 0;

        // Debug
        private const string LOG_PREFIX = "[BorderCharacterController]";

        #endregion

        #region Unity Lifecycle

        private void Awake()
        {
            LogDebug("Khởi tạo BorderCharacterController...");
            InitializeComponents();
            ValidateConfiguration();
        }

        private void Start()
        {
            LogDebug("Bắt đầu setup BorderCharacterController...");
            SetupConfiguration();
            RegisterEvents();
            LogDebug("BorderCharacterController đã sẵn sàng!");
        }

        private void Update()
        {
            UpdateMovementEvents();
            UpdateGroundedEvents();
            UpdateCrouchEvents();
            UpdateJumpEvents();

            if (enableProfiling)
            {
                ProfilePerformance();
            }
        }

        private void OnDestroy()
        {
            LogDebug("Hủy BorderCharacterController...");
            UnregisterEvents();
        }

        private void OnValidate()
        {
            // Validate configuration khi thay đổi trong Inspector
            if (characterConfig != null)
            {
                characterConfig.ValidateConfiguration();
            }
        }

        #endregion

        #region Initialization

        /// <summary>
        /// Khởi tạo và validate các components cần thiết
        /// </summary>
        private void InitializeComponents()
        {
            // Get or add required components
            if (nappinController == null)
                nappinController = GetComponent<CharacterManager>();

            if (inputManager == null)
                inputManager = GetComponent<BorderInputManager>();

            if (eventSystem == null)
                eventSystem = GetComponent<BorderEventSystem>();

            // Validate critical components
            if (nappinController == null)
            {
                LogError("CharacterManager component không tìm thấy! Vui lòng thêm component này.");
                enabled = false;
                return;
            }

            if (inputManager == null)
            {
                LogWarning("BorderInputManager không tìm thấy. Sẽ tạo tự động...");
                inputManager = gameObject.AddComponent<BorderInputManager>();
            }

            if (eventSystem == null)
            {
                LogWarning("BorderEventSystem không tìm thấy. Sẽ tạo tự động...");
                eventSystem = gameObject.AddComponent<BorderEventSystem>();
            }

            LogDebug("Tất cả components đã được khởi tạo thành công.");
        }

        /// <summary>
        /// Validate và tạo configuration mặc định nếu cần
        /// </summary>
        private void ValidateConfiguration()
        {
            if (characterConfig == null)
            {
                LogWarning("Không có cấu hình nào được gán. Đang tạo cấu hình mặc định...");
                characterConfig = CreateDefaultConfig();
            }
            else
            {
                characterConfig.ValidateConfiguration();
                LogDebug($"Đã load cấu hình: {characterConfig.ConfigName}");
            }
        }

        /// <summary>
        /// Áp dụng cấu hình cho character
        /// </summary>
        private void SetupConfiguration()
        {
            if (characterConfig != null && nappinController != null)
            {
                ApplyMovementSettings();
                ApplyCameraSettings();
                ApplyInteractionSettings();
                LogDebug("Đã áp dụng cấu hình thành công.");
            }
        }

        #endregion

        #region Configuration Application

        /// <summary>
        /// Áp dụng cài đặt di chuyển từ configuration
        /// </summary>
        private void ApplyMovementSettings()
        {
            var settings = characterConfig.movementSettings;
            
            nappinController.movementSpeed = settings.tocDoDiBo;
            nappinController.sprintSpeed = settings.tocDoChay;
            nappinController.crouchSpeedMultiplier = settings.heSoTocDoKhiCui;
            
            // Apply advanced settings
            nappinController.dampSpeedUp = settings.thoiGianTangToc;
            nappinController.dampSpeedDown = settings.thoiGianGiamToc;
            nappinController.frictionAgainstFloor = settings.maSatMatDat;
            nappinController.frictionAgainstWall = settings.maSatTuong;

            LogDebug($"Đã áp dụng cài đặt di chuyển - Tốc độ đi bộ: {settings.tocDoDiBo}, Tốc độ chạy: {settings.tocDoChay}");
        }

        /// <summary>
        /// Áp dụng cài đặt camera từ configuration
        /// </summary>
        private void ApplyCameraSettings()
        {
            var settings = characterConfig.cameraSettings;
            
            // Apply camera configuration if camera manager exists
            var cameraManager = GetComponent<CameraManager>();
            if (cameraManager != null)
            {
                // Configure camera settings
                // Implementation sẽ được mở rộng trong Phase 2
                LogDebug("Đã áp dụng cài đặt camera.");
            }
        }

        /// <summary>
        /// Áp dụng cài đặt tương tác từ configuration
        /// </summary>
        private void ApplyInteractionSettings()
        {
            var settings = characterConfig.interactionSettings;
            
            // Configure interaction range, UI settings, etc.
            // Implementation sẽ được mở rộng trong Phase 2
            LogDebug("Đã áp dụng cài đặt tương tác.");
        }

        #endregion

        #region Event Management

        /// <summary>
        /// Đăng ký events với Nappin controller
        /// </summary>
        private void RegisterEvents()
        {
            if (nappinController.OnJump != null)
                nappinController.OnJump.AddListener(HandleJumpEvent);

            if (nappinController.OnLand != null)
                nappinController.OnLand.AddListener(HandleLandEvent);

            LogDebug("Đã đăng ký tất cả events.");
        }

        /// <summary>
        /// Hủy đăng ký events để tránh memory leaks
        /// </summary>
        private void UnregisterEvents()
        {
            if (nappinController != null)
            {
                if (nappinController.OnJump != null)
                    nappinController.OnJump.RemoveListener(HandleJumpEvent);

                if (nappinController.OnLand != null)
                    nappinController.OnLand.RemoveListener(HandleLandEvent);
            }

            LogDebug("Đã hủy đăng ký tất cả events.");
        }

        /// <summary>
        /// Xử lý sự kiện nhảy từ Nappin
        /// </summary>
        private void HandleJumpEvent()
        {
            OnBatDauNhay?.Invoke();
            LogDebug("Nhân vật bắt đầu nhảy");
        }

        /// <summary>
        /// Xử lý sự kiện hạ cánh từ Nappin
        /// </summary>
        private void HandleLandEvent()
        {
            OnHaCanh?.Invoke();
            LogDebug("Nhân vật hạ cánh");
        }

        #endregion

        #region State Monitoring

        /// <summary>
        /// Theo dõi và trigger events cho movement
        /// </summary>
        private void UpdateMovementEvents()
        {
            bool isCurrentlyMoving = DangDiChuyen;
            
            if (isCurrentlyMoving && !wasMoving)
            {
                OnBatDauDiChuyen?.Invoke();
                LogDebug("Bắt đầu di chuyển");
            }
            else if (!isCurrentlyMoving && wasMoving)
            {
                OnKetThucDiChuyen?.Invoke();
                LogDebug("Kết thúc di chuyển");
            }
            
            wasMoving = isCurrentlyMoving;
        }

        /// <summary>
        /// Theo dõi trạng thái grounded
        /// </summary>
        private void UpdateGroundedEvents()
        {
            bool isCurrentlyGrounded = DangDungTrenDat;
            
            if (!isCurrentlyGrounded && wasGrounded)
            {
                LogDebug("Rời khỏi mặt đất");
            }
            else if (isCurrentlyGrounded && !wasGrounded)
            {
                LogDebug("Chạm đất");
            }
            
            wasGrounded = isCurrentlyGrounded;
        }

        /// <summary>
        /// Theo dõi trạng thái cúi
        /// </summary>
        private void UpdateCrouchEvents()
        {
            bool isCurrentlyCrouching = DangCui;
            
            if (isCurrentlyCrouching != wasCrouching)
            {
                OnThayDoiTrangThaiCui?.Invoke(isCurrentlyCrouching);
                
                string action = isCurrentlyCrouching ? "bắt đầu cúi" : "đứng dậy";
                LogDebug($"Nhân vật {action}");
            }
            
            wasCrouching = isCurrentlyCrouching;
        }

        /// <summary>
        /// Theo dõi trạng thái nhảy
        /// </summary>
        private void UpdateJumpEvents()
        {
            bool isCurrentlyJumping = DangNhay;
            
            if (isCurrentlyJumping && !wasJumping)
            {
                LogDebug("Bắt đầu nhảy");
            }
            else if (!isCurrentlyJumping && wasJumping)
            {
                LogDebug("Kết thúc nhảy");
            }
            
            wasJumping = isCurrentlyJumping;
        }

        #endregion

        #region Public API Methods

        /// <summary>
        /// Thiết lập cấu hình mới cho character
        /// </summary>
        /// <param name="cauHinhMoi">Cấu hình mới cần áp dụng</param>
        public void ThietLapCauHinh(BorderCharacterConfig cauHinhMoi)
        {
            if (cauHinhMoi == null)
            {
                LogError("Cấu hình không được để trống!");
                return;
            }

            characterConfig = cauHinhMoi;
            SetupConfiguration();
            
            LogDebug($"Đã áp dụng cấu hình mới: {cauHinhMoi.ConfigName}");
        }

        /// <summary>
        /// Bật/tắt chế độ debug
        /// </summary>
        /// <param name="batTat">true để bật, false để tắt</param>
        public void BatTatCheDoDebug(bool batTat)
        {
            enableDebugMode = batTat;
            
            if (enableDebugMode)
                LogDebug("Chế độ debug đã được bật");
            else
                Debug.Log($"{LOG_PREFIX} Chế độ debug đã được tắt");
        }

        /// <summary>
        /// Lấy thông tin trạng thái hiện tại của character
        /// </summary>
        /// <returns>Thông tin chi tiết về trạng thái nhân vật</returns>
        public ThongTinTrangThai LayThongTinTrangThai()
        {
            return new ThongTinTrangThai
            {
                dangDungTrenDat = DangDungTrenDat,
                dangDiChuyen = DangDiChuyen,
                dangNhay = DangNhay,
                dangCui = DangCui,
                dangChamTuong = DangChamTuong,
                vanToc = VanToc,
                viTri = ViTri,
                huongNhin = HuongNhin
            };
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Tạo cấu hình mặc định
        /// </summary>
        /// <returns>Cấu hình mặc định</returns>
        private BorderCharacterConfig CreateDefaultConfig()
        {
            var defaultConfig = DefaultBorderCharacterConfig.CreateDefault();
            LogDebug("Đã tạo cấu hình mặc định");
            return defaultConfig;
        }

        /// <summary>
        /// Theo dõi hiệu suất
        /// </summary>
        private void ProfilePerformance()
        {
            frameCount++;
            float currentTime = Time.realtimeSinceStartup;
            
            if (currentTime - lastFrameTime >= 1f) // Mỗi giây
            {
                float fps = frameCount / (currentTime - lastFrameTime);
                LogDebug($"FPS: {fps:F1}");
                
                frameCount = 0;
                lastFrameTime = currentTime;
            }
        }

        /// <summary>
        /// Log debug message nếu debug mode được bật
        /// </summary>
        /// <param name="message">Thông điệp cần log</param>
        private void LogDebug(string message)
        {
            if (enableDebugMode)
                Debug.Log($"{LOG_PREFIX} {message}");
        }

        /// <summary>
        /// Log warning message
        /// </summary>
        /// <param name="message">Thông điệp warning</param>
        private void LogWarning(string message)
        {
            Debug.LogWarning($"{LOG_PREFIX} {message}");
        }

        /// <summary>
        /// Log error message
        /// </summary>
        /// <param name="message">Thông điệp lỗi</param>
        private void LogError(string message)
        {
            Debug.LogError($"{LOG_PREFIX} {message}");
        }

        #endregion
    }

    /// <summary>
    /// Struct chứa thông tin trạng thái của character
    /// </summary>
    [System.Serializable]
    public struct ThongTinTrangThai
    {
        [Tooltip("Nhân vật có đang đứng trên mặt đất không?")]
        public bool dangDungTrenDat;
        
        [Tooltip("Nhân vật có đang di chuyển không?")]
        public bool dangDiChuyen;
        
        [Tooltip("Nhân vật có đang nhảy không?")]
        public bool dangNhay;
        
        [Tooltip("Nhân vật có đang cúi không?")]
        public bool dangCui;
        
        [Tooltip("Nhân vật có đang chạm tường không?")]
        public bool dangChamTuong;
        
        [Tooltip("Vận tốc hiện tại")]
        public Vector3 vanToc;
        
        [Tooltip("Vị trí hiện tại")]
        public Vector3 viTri;
        
        [Tooltip("Hướng nhìn hiện tại")]
        public Vector3 huongNhin;
    }
}
