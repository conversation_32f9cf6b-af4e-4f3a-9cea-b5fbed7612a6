# Roadmap Triển Khai Border Character System
## Chi Tiết Kỹ Thuật và Code Implementation

---

## 🚀 Phase 1: Foundation Setup (Tuần 1-2)

### Milestone 1.1: Project Structure & Namespace Setup

#### C<PERSON>u <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>
```
Assets/
├── BorderCharacterSystem/
│   ├── Core/
│   │   ├── BorderCharacterController.cs
│   │   ├── BorderCharacterConfig.cs
│   │   ├── BorderInputManager.cs
│   │   └── BorderEventSystem.cs
│   ├── Components/
│   │   ├── Movement/
│   │   ├── Interaction/
│   │   └── Camera/
│   ├── Configuration/
│   │   ├── ScriptableObjects/
│   │   └── Presets/
│   ├── Localization/
│   │   ├── Languages/
│   │   └── Scripts/
│   ├── Examples/
│   │   ├── Scenes/
│   │   ├── Prefabs/
│   │   └── Scripts/
│   ├── Documentation/
│   │   ├── API/
│   │   ├── Tutorials/
│   │   └── Videos/
│   └── Tests/
│       ├── Editor/
│       └── Runtime/
```

#### Namespace Convention
```csharp
// Core namespace
namespace BorderCharacterSystem.Core { }

// Component namespaces
namespace BorderCharacterSystem.Movement { }
namespace BorderCharacterSystem.Interaction { }
namespace BorderCharacterSystem.Camera { }

// Configuration namespace
namespace BorderCharacterSystem.Configuration { }

// Localization namespace
namespace BorderCharacterSystem.Localization { }

// Utilities namespace
namespace BorderCharacterSystem.Utilities { }
```

### Milestone 1.2: Core Wrapper Implementation

#### BorderCharacterController.cs - Main Wrapper
```csharp
using UnityEngine;
using UnityEngine.Events;
using PhysicsCharacterController;

namespace BorderCharacterSystem.Core
{
    /// <summary>
    /// Wrapper chính cho Nappin PhysicsCharacterController
    /// Cung cấp interface đơn giản và dễ sử dụng cho developers
    /// </summary>
    [RequireComponent(typeof(CharacterManager))]
    [RequireComponent(typeof(BorderInputManager))]
    public class BorderCharacterController : MonoBehaviour
    {
        [Header("Cấu Hình Hệ Thống")]
        [SerializeField] private BorderCharacterConfig characterConfig;
        [SerializeField] private bool enableDebugMode = false;
        [SerializeField] private bool enableProfiling = false;

        [Header("Component References")]
        [SerializeField] private CharacterManager nappinController;
        [SerializeField] private BorderInputManager inputManager;
        [SerializeField] private BorderEventSystem eventSystem;

        // Component Properties
        public BorderCharacterConfig Config => characterConfig;
        public CharacterManager NappinController => nappinController;
        public BorderInputManager InputManager => inputManager;
        public BorderEventSystem EventSystem => eventSystem;

        // State Properties
        public bool IsGrounded => nappinController.GetGrounded();
        public bool IsMoving => nappinController.axisInput.magnitude > 0.1f;
        public bool IsJumping => nappinController.GetJumping();
        public bool IsCrouching => nappinController.GetCrouching();
        public bool IsTouchingWall => nappinController.GetTouchingWall();

        // Movement Properties
        public Vector3 Velocity => nappinController.rigidbody.linearVelocity;
        public Vector3 Position => transform.position;
        public Vector3 Forward => transform.forward;

        // Events - Sử dụng tiếng Việt cho user-friendly
        [Header("Sự Kiện Di Chuyển")]
        public UnityEvent OnBatDauDiChuyen; // OnMovementStarted
        public UnityEvent OnKetThucDiChuyen; // OnMovementStopped
        public UnityEvent OnBatDauNhay; // OnJumpStarted
        public UnityEvent OnHaCanh; // OnLanded
        public UnityEvent<bool> OnThayDoiTrangThaiCui; // OnCrouchToggled

        [Header("Sự Kiện Tương Tác")]
        public UnityEvent<GameObject> OnTuongTacVoiDoiTuong; // OnInteractWithObject
        public UnityEvent<string> OnNhanVatPham; // OnItemReceived

        // Private fields
        private bool wasMoving = false;
        private bool wasGrounded = true;
        private bool wasCrouching = false;

        #region Unity Lifecycle

        private void Awake()
        {
            InitializeComponents();
            ValidateConfiguration();
        }

        private void Start()
        {
            SetupConfiguration();
            RegisterEvents();
        }

        private void Update()
        {
            UpdateMovementEvents();
            UpdateGroundedEvents();
            UpdateCrouchEvents();

            if (enableProfiling)
            {
                ProfilePerformance();
            }
        }

        private void OnDestroy()
        {
            UnregisterEvents();
        }

        #endregion

        #region Initialization

        private void InitializeComponents()
        {
            // Get or add required components
            if (nappinController == null)
                nappinController = GetComponent<CharacterManager>();

            if (inputManager == null)
                inputManager = GetComponent<BorderInputManager>();

            if (eventSystem == null)
                eventSystem = GetComponent<BorderEventSystem>();

            // Validate components
            if (nappinController == null)
                Debug.LogError("[BorderCharacterController] CharacterManager component not found!");

            if (inputManager == null)
                Debug.LogError("[BorderCharacterController] BorderInputManager component not found!");
        }

        private void ValidateConfiguration()
        {
            if (characterConfig == null)
            {
                Debug.LogWarning("[BorderCharacterController] No configuration assigned. Creating default config.");
                characterConfig = CreateDefaultConfig();
            }
        }

        private void SetupConfiguration()
        {
            if (characterConfig != null)
            {
                ApplyMovementSettings();
                ApplyCameraSettings();
                ApplyInteractionSettings();
            }
        }

        #endregion

        #region Configuration Application

        private void ApplyMovementSettings()
        {
            var settings = characterConfig.movementSettings;
            
            nappinController.movementSpeed = settings.walkSpeed;
            nappinController.sprintSpeed = settings.runSpeed;
            nappinController.jumpVelocity = settings.jumpForce;
            nappinController.crouchSpeedMultiplier = settings.crouchSpeedMultiplier;
            
            // Apply advanced settings
            nappinController.dampSpeedUp = settings.accelerationDamping;
            nappinController.dampSpeedDown = settings.decelerationDamping;
            nappinController.frictionAgainstFloor = settings.groundFriction;
            nappinController.frictionAgainstWall = settings.wallFriction;
        }

        private void ApplyCameraSettings()
        {
            var settings = characterConfig.cameraSettings;
            
            // Apply camera configuration if camera manager exists
            var cameraManager = GetComponent<CameraManager>();
            if (cameraManager != null)
            {
                // Configure camera settings
                // Implementation depends on camera system requirements
            }
        }

        private void ApplyInteractionSettings()
        {
            var settings = characterConfig.interactionSettings;
            
            // Configure interaction range, UI settings, etc.
            // Implementation will be added in Phase 2
        }

        #endregion

        #region Event Management

        private void RegisterEvents()
        {
            // Register with Nappin events if available
            if (nappinController.OnJump != null)
                nappinController.OnJump.AddListener(HandleJumpEvent);

            if (nappinController.OnLand != null)
                nappinController.OnLand.AddListener(HandleLandEvent);
        }

        private void UnregisterEvents()
        {
            // Unregister events to prevent memory leaks
            if (nappinController != null)
            {
                if (nappinController.OnJump != null)
                    nappinController.OnJump.RemoveListener(HandleJumpEvent);

                if (nappinController.OnLand != null)
                    nappinController.OnLand.RemoveListener(HandleLandEvent);
            }
        }

        private void HandleJumpEvent()
        {
            OnBatDauNhay?.Invoke();
            
            if (enableDebugMode)
                Debug.Log("[BorderCharacterController] Nhân vật bắt đầu nhảy");
        }

        private void HandleLandEvent()
        {
            OnHaCanh?.Invoke();
            
            if (enableDebugMode)
                Debug.Log("[BorderCharacterController] Nhân vật hạ cánh");
        }

        #endregion

        #region State Monitoring

        private void UpdateMovementEvents()
        {
            bool isCurrentlyMoving = IsMoving;
            
            if (isCurrentlyMoving && !wasMoving)
            {
                OnBatDauDiChuyen?.Invoke();
                if (enableDebugMode)
                    Debug.Log("[BorderCharacterController] Bắt đầu di chuyển");
            }
            else if (!isCurrentlyMoving && wasMoving)
            {
                OnKetThucDiChuyen?.Invoke();
                if (enableDebugMode)
                    Debug.Log("[BorderCharacterController] Kết thúc di chuyển");
            }
            
            wasMoving = isCurrentlyMoving;
        }

        private void UpdateGroundedEvents()
        {
            bool isCurrentlyGrounded = IsGrounded;
            
            if (!isCurrentlyGrounded && wasGrounded)
            {
                // Left ground (started falling or jumping)
                if (enableDebugMode)
                    Debug.Log("[BorderCharacterController] Rời khỏi mặt đất");
            }
            else if (isCurrentlyGrounded && !wasGrounded)
            {
                // Landed
                if (enableDebugMode)
                    Debug.Log("[BorderCharacterController] Chạm đất");
            }
            
            wasGrounded = isCurrentlyGrounded;
        }

        private void UpdateCrouchEvents()
        {
            bool isCurrentlyCrouching = IsCrouching;
            
            if (isCurrentlyCrouching != wasCrouching)
            {
                OnThayDoiTrangThaiCui?.Invoke(isCurrentlyCrouching);
                
                if (enableDebugMode)
                {
                    string action = isCurrentlyCrouching ? "bắt đầu cúi" : "đứng dậy";
                    Debug.Log($"[BorderCharacterController] Nhân vật {action}");
                }
            }
            
            wasCrouching = isCurrentlyCrouching;
        }

        #endregion

        #region Public API Methods

        /// <summary>
        /// Thiết lập cấu hình mới cho character
        /// </summary>
        /// <param name="newConfig">Cấu hình mới</param>
        public void SetConfiguration(BorderCharacterConfig newConfig)
        {
            characterConfig = newConfig;
            SetupConfiguration();
            
            if (enableDebugMode)
                Debug.Log("[BorderCharacterController] Đã áp dụng cấu hình mới");
        }

        /// <summary>
        /// Bật/tắt chế độ debug
        /// </summary>
        /// <param name="enabled">Trạng thái debug</param>
        public void SetDebugMode(bool enabled)
        {
            enableDebugMode = enabled;
            
            if (enableDebugMode)
                Debug.Log("[BorderCharacterController] Chế độ debug đã được bật");
        }

        /// <summary>
        /// Lấy thông tin trạng thái hiện tại của character
        /// </summary>
        /// <returns>Thông tin trạng thái</returns>
        public CharacterState GetCurrentState()
        {
            return new CharacterState
            {
                isGrounded = IsGrounded,
                isMoving = IsMoving,
                isJumping = IsJumping,
                isCrouching = IsCrouching,
                isTouchingWall = IsTouchingWall,
                velocity = Velocity,
                position = Position
            };
        }

        #endregion

        #region Utility Methods

        private BorderCharacterConfig CreateDefaultConfig()
        {
            // Create default configuration
            // Implementation will be completed in next milestone
            return ScriptableObject.CreateInstance<BorderCharacterConfig>();
        }

        private void ProfilePerformance()
        {
            // Performance profiling implementation
            // Will be added in Phase 3
        }

        #endregion
    }

    /// <summary>
    /// Struct chứa thông tin trạng thái của character
    /// </summary>
    [System.Serializable]
    public struct CharacterState
    {
        public bool isGrounded;
        public bool isMoving;
        public bool isJumping;
        public bool isCrouching;
        public bool isTouchingWall;
        public Vector3 velocity;
        public Vector3 position;
    }
}
```

### Milestone 1.3: Configuration System

#### BorderCharacterConfig.cs - ScriptableObject Configuration
```csharp
using UnityEngine;

namespace BorderCharacterSystem.Configuration
{
    /// <summary>
    /// Cấu hình chính cho Border Character System
    /// Sử dụng ScriptableObject để dễ dàng tạo và quản lý presets
    /// </summary>
    [CreateAssetMenu(fileName = "BorderCharacterConfig", 
                     menuName = "Border Character System/Character Config",
                     order = 1)]
    public class BorderCharacterConfig : ScriptableObject
    {
        [Header("Thông Tin Cấu Hình")]
        [SerializeField] private string configName = "Default Config";
        [SerializeField] private string description = "Cấu hình mặc định cho Border Character System";
        [SerializeField] private string version = "1.0.0";

        [Header("Cài Đặt Di Chuyển")]
        public MovementSettings movementSettings = new MovementSettings();

        [Header("Cài Đặt Nhảy")]
        public JumpSettings jumpSettings = new JumpSettings();

        [Header("Cài Đặt Camera")]
        public CameraSettings cameraSettings = new CameraSettings();

        [Header("Cài Đặt Tương Tác")]
        public InteractionSettings interactionSettings = new InteractionSettings();

        [Header("Cài Đặt Debug")]
        public DebugSettings debugSettings = new DebugSettings();

        [Header("Cài Đặt Localization")]
        public LocalizationSettings localizationSettings = new LocalizationSettings();

        // Properties
        public string ConfigName => configName;
        public string Description => description;
        public string Version => version;

        /// <summary>
        /// Tạo bản sao của cấu hình hiện tại
        /// </summary>
        /// <returns>Bản sao của cấu hình</returns>
        public BorderCharacterConfig Clone()
        {
            var clone = CreateInstance<BorderCharacterConfig>();
            clone.configName = configName + " (Copy)";
            clone.description = description;
            clone.version = version;
            clone.movementSettings = movementSettings.Clone();
            clone.jumpSettings = jumpSettings.Clone();
            clone.cameraSettings = cameraSettings.Clone();
            clone.interactionSettings = interactionSettings.Clone();
            clone.debugSettings = debugSettings.Clone();
            clone.localizationSettings = localizationSettings.Clone();
            return clone;
        }

        /// <summary>
        /// Validate cấu hình và hiển thị warnings nếu cần
        /// </summary>
        public void ValidateConfiguration()
        {
            movementSettings.Validate();
            jumpSettings.Validate();
            cameraSettings.Validate();
            interactionSettings.Validate();
            debugSettings.Validate();
            localizationSettings.Validate();
        }

        private void OnValidate()
        {
            ValidateConfiguration();
        }
    }
}
```

---

## 📋 Deliverables Phase 1

### Tuần 1 Deliverables:
- ✅ **BorderCharacterController.cs**: Main wrapper với basic functionality
- ✅ **BorderCharacterConfig.cs**: ScriptableObject configuration system
- ✅ **Project Structure**: Organized folder structure và namespaces
- ✅ **Basic Events**: Enhanced event system với tiếng Việt
- ✅ **Documentation**: Inline documentation và basic README

### Tuần 2 Deliverables:
- ✅ **BorderInputManager.cs**: Enhanced input management
- ✅ **BorderEventSystem.cs**: Advanced event system
- ✅ **Configuration Settings**: Detailed settings classes
- ✅ **Integration Tests**: Basic integration với Nappin asset
- ✅ **Validation System**: Configuration validation

---

## 🎯 Success Metrics Phase 1

### Technical Metrics:
- [ ] 100% compatibility với existing Nappin asset
- [ ] Zero breaking changes to existing functionality
- [ ] < 5ms additional overhead per frame
- [ ] Memory usage < 10MB additional

### User Experience Metrics:
- [ ] Setup time < 2 minutes from import
- [ ] Configuration creation < 30 seconds
- [ ] Clear error messages in Vietnamese
- [ ] Intuitive inspector interface

### Code Quality Metrics:
- [ ] 100% inline documentation coverage
- [ ] Zero compiler warnings
- [ ] Consistent naming conventions
- [ ] Proper namespace organization

---

**Tiếp Theo**: Phase 2 sẽ focus vào implementation của movement components, interaction system, và camera enhancements. Mỗi component sẽ được thiết kế modular và có thể test independently.
