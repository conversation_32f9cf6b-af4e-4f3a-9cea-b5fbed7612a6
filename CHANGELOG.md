# Lịch Sử Thay Đổi - Boder Documentary

Tất cả các thay đổi quan trọng của dự án "Boder Documentary" sẽ được ghi lại trong file này.

Format dựa trên [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
và dự án này tuân theo [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Planned
- <PERSON>ệ thống nhân vật DocumentaryCharacterController
- <PERSON><PERSON> thống camera documentary và photo mode
- Hệ thống tương tác và khám phá
- UI cơ bản cho prototype
- Hệ thống lưu/tải game

## [0.1.0] - 2025-01-XX - Foundation Setup

### Added
- Tạo kế hoạch phát triển prototype chi tiết
- <PERSON><PERSON><PERSON> ngh<PERSON>a cấu trú<PERSON> thư mục dự án
- <PERSON><PERSON><PERSON> định dependencies và integration vớ<PERSON> Nappin PhysicsCharacterController
- <PERSON><PERSON><PERSON><PERSON> lập timeline phát triển 8 tuần
- Định nghĩa architecture patterns và coding standards

### Technical Details
- Unity Version: 6000.0.50f1
- Render Pipeline: Universal Render Pipeline (URP) 17.0.4
- Input System: Unity Input System 1.14.0
- Camera System: Cinemachine 3.1.3
- Platform Target: PC (Windows)

### Dependencies Confirmed
- ✅ Unity Input System (1.14.0)
- ✅ Cinemachine (3.1.3) 
- ✅ Universal Render Pipeline (17.0.4)
- ✅ Visual Effect Graph (17.0.4)
- ✅ Timeline (1.8.7)

## [0.0.1] - 2025-01-XX - Project Initialization

### Added
- Khởi tạo repository dự án
- Tạo workspace cơ bản
- Setup Unity project với URP
- Import Nappin PhysicsCharacterController system

---

## Template cho các phiên bản tương lai

### [X.Y.Z] - YYYY-MM-DD - Tên Milestone

#### Added (Tính năng mới)
- Tính năng A với mô tả chi tiết
- Component B được implement
- System C được tích hợp

#### Changed (Thay đổi)
- Cải thiện performance của System X
- Refactor code structure cho Module Y
- Update UI/UX cho Feature Z

#### Fixed (Sửa lỗi)
- Sửa lỗi crash khi interact với Object A
- Fix memory leak trong System B
- Khắc phục input lag trong Character Controller

#### Removed (Loại bỏ)
- Loại bỏ deprecated API X
- Remove unused assets
- Clean up obsolete code

#### Security (Bảo mật)
- Cải thiện save file security
- Update dependencies với security patches

#### Performance (Hiệu suất)
- Optimize rendering pipeline
- Improve loading times
- Reduce memory usage

#### Technical Notes (Ghi chú kỹ thuật)
- Breaking changes và migration guide
- API changes và deprecation notices
- Known issues và workarounds

#### Links
- [Commit Hash](link-to-commit)
- [Pull Request #XX](link-to-pr)
- [Issue #XX](link-to-issue)

---

## Quy Tắc Ghi Changelog

### Format Entry
```markdown
### [Version] - YYYY-MM-DD - Milestone Name

#### Added
- [Component/System] Mô tả tính năng mới
- [UI/UX] Cải thiện giao diện người dùng
- [Feature] Tính năng gameplay mới

#### Changed  
- [Performance] Cải thiện hiệu suất
- [Refactor] Tái cấu trúc code
- [Update] Cập nhật dependencies

#### Fixed
- [Bug] Mô tả lỗi đã sửa
- [Issue] Khắc phục vấn đề
- [Hotfix] Sửa lỗi khẩn cấp

#### Removed
- [Deprecated] Loại bỏ tính năng cũ
- [Cleanup] Dọn dẹp code không dùng
```

### Nguyên Tắc Ghi Chép
1. **Ngôn ngữ:** Sử dụng tiếng Việt cho mô tả, tiếng Anh cho technical terms
2. **Chi tiết:** Ghi đủ thông tin để developer khác hiểu được thay đổi
3. **Liên kết:** Bao gồm links đến commits, PRs, issues liên quan
4. **Thời gian:** Ghi rõ ngày tháng thực hiện thay đổi
5. **Tác động:** Mô tả tác động của thay đổi đến hệ thống tổng thể

### Versioning Strategy
- **Major (X.0.0):** Breaking changes, major milestones
- **Minor (X.Y.0):** New features, significant improvements
- **Patch (X.Y.Z):** Bug fixes, small improvements

### Review Process
1. Mỗi thay đổi phải được review trước khi merge
2. Changelog entry phải được approve cùng với code changes
3. Regular review changelog để ensure consistency
4. Update changelog trước khi release mỗi version
