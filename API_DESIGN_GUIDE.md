# Border Character System - API Design Guide
## Hướng Dẫn Thiết Kế API và Sử Dụng

---

## 🎯 Nguyên Tắc Thiết Kế API

### 1. Simplicity First (Đơn Giản Trước Tiên)
- **Easy Setup**: Chỉ cần drag & drop prefab để bắt đầu
- **Sensible Defaults**: C<PERSON>u hình mặc định hoạt động tốt cho 80% use cases
- **Progressive Disclosure**: Advanced features chỉ hiện khi cần thiết

### 2. Vietnamese-First Approach
- **Property Names**: Sử dụng tiếng Việt cho user-facing properties
- **Event Names**: Events có tên tiếng Việt dễ hiểu
- **Documentation**: Tất cả documentation bằng tiếng Việt
- **Error Messages**: Thông báo lỗi bằng tiếng Việt

### 3. Consistency & Predictability
- **Naming Conventions**: Consistent naming pattern
- **Parameter Order**: Predictable parameter ordering
- **Return Types**: Consistent return type patterns
- **Error Handling**: Unified error handling approach

---

## 🏗️ Core API Structure

### BorderCharacterController - Main Interface

```csharp
public class BorderCharacterController : MonoBehaviour
{
    // === PROPERTIES CƠ BẢN ===
    
    /// <summary>Nhân vật có đang đứng trên mặt đất không?</summary>
    public bool DangDungTrenDat { get; }
    
    /// <summary>Nhân vật có đang di chuyển không?</summary>
    public bool DangDiChuyen { get; }
    
    /// <summary>Nhân vật có đang nhảy không?</summary>
    public bool DangNhay { get; }
    
    /// <summary>Nhân vật có đang cúi không?</summary>
    public bool DangCui { get; }
    
    /// <summary>Vận tốc hiện tại của nhân vật</summary>
    public Vector3 VanToc { get; }
    
    /// <summary>Vị trí hiện tại của nhân vật</summary>
    public Vector3 ViTri { get; }
    
    // === METHODS CƠ BẢN ===
    
    /// <summary>
    /// Thiết lập cấu hình mới cho nhân vật
    /// </summary>
    /// <param name="cauHinhMoi">Cấu hình mới cần áp dụng</param>
    public void ThietLapCauHinh(BorderCharacterConfig cauHinhMoi);
    
    /// <summary>
    /// Bật/tắt chế độ debug
    /// </summary>
    /// <param name="batTat">true để bật, false để tắt</param>
    public void BatTatCheDoDebug(bool batTat);
    
    /// <summary>
    /// Lấy thông tin trạng thái hiện tại
    /// </summary>
    /// <returns>Thông tin chi tiết về trạng thái nhân vật</returns>
    public ThongTinTrangThai LayThongTinTrangThai();
    
    // === EVENTS ===
    
    /// <summary>Sự kiện khi nhân vật bắt đầu di chuyển</summary>
    public UnityEvent OnBatDauDiChuyen;
    
    /// <summary>Sự kiện khi nhân vật dừng di chuyển</summary>
    public UnityEvent OnDungDiChuyen;
    
    /// <summary>Sự kiện khi nhân vật bắt đầu nhảy</summary>
    public UnityEvent OnBatDauNhay;
    
    /// <summary>Sự kiện khi nhân vật hạ cánh</summary>
    public UnityEvent OnHaCanh;
    
    /// <summary>Sự kiện khi thay đổi trạng thái cúi</summary>
    public UnityEvent<bool> OnThayDoiTrangThaiCui;
}
```

### Configuration System - Hệ Thống Cấu Hình

```csharp
[CreateAssetMenu(menuName = "Border Character System/Cấu Hình Nhân Vật")]
public class BorderCharacterConfig : ScriptableObject
{
    [Header("Cài Đặt Di Chuyển")]
    public CaiDatDiChuyen caiDatDiChuyen;
    
    [Header("Cài Đặt Nhảy")]
    public CaiDatNhay caiDatNhay;
    
    [Header("Cài Đặt Camera")]
    public CaiDatCamera caiDatCamera;
    
    [Header("Cài Đặt Tương Tác")]
    public CaiDatTuongTac caiDatTuongTac;
}

[System.Serializable]
public class CaiDatDiChuyen
{
    [Header("Tốc Độ")]
    [Range(1f, 20f)]
    [Tooltip("Tốc độ đi bộ của nhân vật")]
    public float tocDoDiBo = 5f;
    
    [Range(5f, 30f)]
    [Tooltip("Tốc độ chạy của nhân vật")]
    public float tocDoChay = 10f;
    
    [Range(0.1f, 1f)]
    [Tooltip("Hệ số tốc độ khi cúi")]
    public float heSoTocDoKhiCui = 0.5f;
    
    [Header("Gia Tốc")]
    [Range(0.01f, 1f)]
    [Tooltip("Thời gian tăng tốc")]
    public float thoiGianTangToc = 0.2f;
    
    [Range(0.01f, 1f)]
    [Tooltip("Thời gian giảm tốc")]
    public float thoiGianGiamToc = 0.1f;
    
    [Header("Ma Sát")]
    [Range(0f, 1f)]
    [Tooltip("Ma sát với mặt đất")]
    public float maSatMatDat = 0.3f;
    
    [Range(0f, 1f)]
    [Tooltip("Ma sát với tường")]
    public float maSatTuong = 0.8f;
}

[System.Serializable]
public class CaiDatNhay
{
    [Header("Lực Nhảy")]
    [Range(5f, 30f)]
    [Tooltip("Lực nhảy cơ bản")]
    public float lucNhayCoBan = 15f;
    
    [Range(1f, 5f)]
    [Tooltip("Hệ số nhảy từ tường")]
    public float heSoNhayTuTuong = 1.5f;
    
    [Header("Trọng Lực")]
    [Range(1f, 5f)]
    [Tooltip("Hệ số trọng lực khi rơi")]
    public float heSoTrongLucKhiRoi = 2f;
    
    [Range(1f, 10f)]
    [Tooltip("Hệ số trọng lực khi giữ nút nhảy")]
    public float heSoTrongLucKhiGiuNutNhay = 5f;
}
```

---

## 📚 Usage Examples - Ví Dụ Sử Dụng

### Example 1: Basic Setup - Thiết Lập Cơ Bản

```csharp
using UnityEngine;
using BorderCharacterSystem.Core;

public class VidDuCoBan : MonoBehaviour
{
    [Header("References")]
    public BorderCharacterController nhanVat;
    public BorderCharacterConfig cauHinhMacDinh;
    
    void Start()
    {
        // Thiết lập cấu hình
        nhanVat.ThietLapCauHinh(cauHinhMacDinh);
        
        // Đăng ký events
        nhanVat.OnBatDauDiChuyen.AddListener(XuLyBatDauDiChuyen);
        nhanVat.OnDungDiChuyen.AddListener(XuLyDungDiChuyen);
        nhanVat.OnBatDauNhay.AddListener(XuLyBatDauNhay);
        nhanVat.OnHaCanh.AddListener(XuLyHaCanh);
        
        // Bật debug mode
        nhanVat.BatTatCheDoDebug(true);
    }
    
    void XuLyBatDauDiChuyen()
    {
        Debug.Log("Nhân vật bắt đầu di chuyển!");
        // Có thể phát âm thanh bước chân
        // Có thể tạo hiệu ứng bụi
    }
    
    void XuLyDungDiChuyen()
    {
        Debug.Log("Nhân vật dừng di chuyển!");
        // Dừng âm thanh bước chân
        // Dừng hiệu ứng bụi
    }
    
    void XuLyBatDauNhay()
    {
        Debug.Log("Nhân vật nhảy!");
        // Phát âm thanh nhảy
        // Tạo hiệu ứng nhảy
    }
    
    void XuLyHaCanh()
    {
        Debug.Log("Nhân vật hạ cánh!");
        // Phát âm thanh hạ cánh
        // Tạo hiệu ứng hạ cánh
    }
}
```

### Example 2: Dynamic Configuration - Cấu Hình Động

```csharp
using UnityEngine;
using BorderCharacterSystem.Core;
using BorderCharacterSystem.Configuration;

public class VidDuCauHinhDong : MonoBehaviour
{
    [Header("Character")]
    public BorderCharacterController nhanVat;
    
    [Header("Configurations")]
    public BorderCharacterConfig cauHinhBinhThuong;
    public BorderCharacterConfig cauHinhNhanh;
    public BorderCharacterConfig cauHinhCham;
    
    [Header("Triggers")]
    public KeyCode phimChuyenDoiNhanh = KeyCode.LeftShift;
    public KeyCode phimChuyenDoiCham = KeyCode.LeftControl;
    
    void Update()
    {
        // Chuyển đổi cấu hình dựa trên input
        if (Input.GetKeyDown(phimChuyenDoiNhanh))
        {
            nhanVat.ThietLapCauHinh(cauHinhNhanh);
            Debug.Log("Chuyển sang chế độ nhanh!");
        }
        else if (Input.GetKeyDown(phimChuyenDoiCham))
        {
            nhanVat.ThietLapCauHinh(cauHinhCham);
            Debug.Log("Chuyển sang chế độ chậm!");
        }
        else if (Input.GetKeyUp(phimChuyenDoiNhanh) || Input.GetKeyUp(phimChuyenDoiCham))
        {
            nhanVat.ThietLapCauHinh(cauHinhBinhThuong);
            Debug.Log("Trở về chế độ bình thường!");
        }
    }
    
    void OnGUI()
    {
        // Hiển thị thông tin trạng thái
        var trangThai = nhanVat.LayThongTinTrangThai();
        
        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.Label("=== THÔNG TIN NHÂN VẬT ===");
        GUILayout.Label($"Đang đứng trên đất: {trangThai.dangDungTrenDat}");
        GUILayout.Label($"Đang di chuyển: {trangThai.dangDiChuyen}");
        GUILayout.Label($"Đang nhảy: {trangThai.dangNhay}");
        GUILayout.Label($"Đang cúi: {trangThai.dangCui}");
        GUILayout.Label($"Vận tốc: {trangThai.vanToc.magnitude:F2} m/s");
        GUILayout.Label($"Vị trí: {trangThai.viTri}");
        GUILayout.EndArea();
    }
}
```

### Example 3: Documentary Game Integration

```csharp
using UnityEngine;
using BorderCharacterSystem.Core;
using BorderCharacterSystem.Interaction;

public class DocumentaryGameController : MonoBehaviour
{
    [Header("Character System")]
    public BorderCharacterController nhanVat;
    public BorderInteractionSystem heTuongTac;
    
    [Header("Documentary Features")]
    public BorderPhotoModeSystem heChupAnh;
    public BorderInventorySystem heKhoVatPham;
    
    [Header("UI")]
    public GameObject uiTuongTac;
    public GameObject uiChupAnh;
    
    void Start()
    {
        // Setup character events
        nhanVat.OnBatDauDiChuyen.AddListener(() => {
            // Ẩn UI khi di chuyển
            uiTuongTac.SetActive(false);
        });
        
        // Setup interaction events
        heTuongTac.OnTimThayDoiTuongTuongTac.AddListener(HienThiUITuongTac);
        heTuongTac.OnMatDoiTuongTuongTac.AddListener(AnUITuongTac);
        heTuongTac.OnTuongTacThanhCong.AddListener(XuLyTuongTacThanhCong);
        
        // Setup photo mode events
        heChupAnh.OnBatDauChupAnh.AddListener(() => {
            uiChupAnh.SetActive(true);
            nhanVat.BatTatCheDoDebug(false); // Tắt debug khi chụp ảnh
        });
        
        heChupAnh.OnKetThucChupAnh.AddListener(() => {
            uiChupAnh.SetActive(false);
            nhanVat.BatTatCheDoDebug(true);
        });
    }
    
    void HienThiUITuongTac(GameObject doiTuong)
    {
        uiTuongTac.SetActive(true);
        // Cập nhật text UI với tên đối tượng
    }
    
    void AnUITuongTac()
    {
        uiTuongTac.SetActive(false);
    }
    
    void XuLyTuongTacThanhCong(string loaiTuongTac, GameObject doiTuong)
    {
        switch (loaiTuongTac)
        {
            case "ThuThapTaiLieu":
                heKhoVatPham.ThemVatPham("TaiLieu", doiTuong.name);
                Debug.Log($"Đã thu thập tài liệu: {doiTuong.name}");
                break;
                
            case "ChupAnh":
                heChupAnh.BatDauChupAnh();
                break;
                
            case "PhongVan":
                // Bắt đầu dialogue system
                Debug.Log($"Bắt đầu phỏng vấn: {doiTuong.name}");
                break;
        }
    }
}
```

---

## 🔧 Advanced API Features

### Custom Event System
```csharp
// Tạo custom events cho game cụ thể
public class DocumentaryEvents : BorderEventSystem
{
    // Events đặc biệt cho documentary game
    public UnityEvent<string> OnTimThayManhMoi;
    public UnityEvent<float> OnCapNhatTienDo;
    public UnityEvent<string> OnHoanThanhMucTieu;
    
    public void BaoCaoTimThayManhMoi(string tenManhMoi)
    {
        OnTimThayManhMoi?.Invoke(tenManhMoi);
        Debug.Log($"Tìm thấy manh mối mới: {tenManhMoi}");
    }
}
```

### Performance Monitoring
```csharp
// Theo dõi performance trong game
public class PerformanceMonitor : MonoBehaviour
{
    public BorderCharacterController nhanVat;
    
    void Update()
    {
        var trangThai = nhanVat.LayThongTinTrangThai();
        
        // Log performance metrics
        if (Time.frameCount % 60 == 0) // Mỗi giây
        {
            Debug.Log($"FPS: {1f / Time.deltaTime:F1}");
            Debug.Log($"Character Position: {trangThai.viTri}");
            Debug.Log($"Character Velocity: {trangThai.vanToc.magnitude:F2}");
        }
    }
}
```

---

## 📖 Best Practices - Thực Hành Tốt Nhất

### 1. Configuration Management
- Tạo separate configs cho different game modes
- Sử dụng descriptive names cho configs
- Test configs thoroughly trước khi deploy

### 2. Event Handling
- Luôn unregister events trong OnDestroy()
- Sử dụng null-conditional operators (?.) cho safety
- Keep event handlers lightweight

### 3. Performance Optimization
- Cache references thay vì GetComponent() calls
- Sử dụng object pooling cho frequent instantiation
- Monitor memory usage với Profiler

### 4. Error Handling
- Validate configurations trong OnValidate()
- Provide meaningful error messages
- Use try-catch cho critical operations

---

**Kết Luận**: API này được thiết kế để dễ sử dụng cho developers Việt Nam, với naming conventions và documentation bằng tiếng Việt. Hệ thống modular cho phép customization sâu khi cần thiết, nhưng vẫn đơn giản cho basic use cases.
