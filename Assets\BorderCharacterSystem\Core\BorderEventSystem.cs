using UnityEngine;
using UnityEngine.Events;
using System.Collections.Generic;

namespace BorderCharacterSystem.Core
{
    /// <summary>
    /// Hệ thống event nâng cao cho Border Character System
    /// Cung cấp event management với tên tiếng Việt và type-safe events
    /// </summary>
    [AddComponentMenu("Border Character System/Border Event System")]
    public class BorderEventSystem : MonoBehaviour
    {
        #region Event Definitions

        /// <summary>
        /// Event khi nhân vật thay đổi trạng thái di chuyển
        /// </summary>
        [System.Serializable]
        public class SuKienThayDoiTrangThaiDiChuyen : UnityEvent<bool> { }

        /// <summary>
        /// Event khi nhân vật tương tác với đối tượng
        /// </summary>
        [System.Serializable]
        public class SuKienTuongTacDoiTuong : UnityEvent<GameObject, string> { }

        /// <summary>
        /// Event khi nhân vật thay đổi vị trí
        /// </summary>
        [System.Serializable]
        public class SuKienThayDoiViTri : UnityEvent<Vector3, Vector3> { }

        /// <summary>
        /// Event khi nhân vật thay đổi tốc độ
        /// </summary>
        [System.Serializable]
        public class SuKienThayDoiTocDo : UnityEvent<float, float> { }

        #endregion

        #region Serialized Events

        [Header("Sự Kiện Di Chuyển")]
        [Tooltip("Sự kiện khi thay đổi trạng thái di chuyển (true = bắt đầu, false = dừng)")]
        public SuKienThayDoiTrangThaiDiChuyen OnThayDoiTrangThaiDiChuyen = new SuKienThayDoiTrangThaiDiChuyen();

        [Tooltip("Sự kiện khi thay đổi vị trí (vị trí cũ, vị trí mới)")]
        public SuKienThayDoiViTri OnThayDoiViTri = new SuKienThayDoiViTri();

        [Tooltip("Sự kiện khi thay đổi tốc độ (tốc độ cũ, tốc độ mới)")]
        public SuKienThayDoiTocDo OnThayDoiTocDo = new SuKienThayDoiTocDo();

        [Header("Sự Kiện Tương Tác")]
        [Tooltip("Sự kiện khi tương tác với đối tượng (đối tượng, loại tương tác)")]
        public SuKienTuongTacDoiTuong OnTuongTacDoiTuong = new SuKienTuongTacDoiTuong();

        [Header("Sự Kiện Hệ Thống")]
        [Tooltip("Sự kiện khi hệ thống khởi tạo")]
        public UnityEvent OnKhoiTaoHeThong = new UnityEvent();

        [Tooltip("Sự kiện khi hệ thống bị lỗi")]
        public UnityEvent<string> OnLoiHeThong = new UnityEvent<string>();

        #endregion

        #region Dynamic Event System

        // Dictionary để lưu trữ dynamic events
        private Dictionary<string, UnityEvent> dynamicEvents = new Dictionary<string, UnityEvent>();
        private Dictionary<string, UnityEvent<object>> dynamicEventsWithData = new Dictionary<string, UnityEvent<object>>();

        #endregion

        #region Event History

        [Header("Event History")]
        [SerializeField]
        [Tooltip("Lưu lịch sử events")]
        private bool luuLichSuEvents = false;

        [SerializeField]
        [Tooltip("Số lượng events tối đa trong lịch sử")]
        private int soLuongEventsToiDa = 100;

        private Queue<EventHistoryEntry> eventHistory = new Queue<EventHistoryEntry>();

        [System.Serializable]
        private struct EventHistoryEntry
        {
            public string tenEvent;
            public float thoiGian;
            public string duLieu;
        }

        #endregion

        #region Properties

        /// <summary>Số lượng events trong lịch sử</summary>
        public int SoLuongEventsLichSu => eventHistory.Count;

        /// <summary>Có đang lưu lịch sử events không?</summary>
        public bool DangLuuLichSu => luuLichSuEvents;

        #endregion

        #region Unity Lifecycle

        private void Awake()
        {
            InitializeEventSystem();
        }

        private void Start()
        {
            OnKhoiTaoHeThong?.Invoke();
            LogEvent("KhoiTaoHeThong", "Hệ thống event đã được khởi tạo");
        }

        #endregion

        #region Initialization

        /// <summary>
        /// Khởi tạo hệ thống event
        /// </summary>
        private void InitializeEventSystem()
        {
            // Initialize dynamic events dictionary
            if (dynamicEvents == null)
                dynamicEvents = new Dictionary<string, UnityEvent>();

            if (dynamicEventsWithData == null)
                dynamicEventsWithData = new Dictionary<string, UnityEvent<object>>();

            // Initialize event history
            if (eventHistory == null)
                eventHistory = new Queue<EventHistoryEntry>();

            Debug.Log("[BorderEventSystem] Hệ thống event đã được khởi tạo");
        }

        #endregion

        #region Public Event Methods

        /// <summary>
        /// Trigger event thay đổi trạng thái di chuyển
        /// </summary>
        /// <param name="dangDiChuyen">Trạng thái di chuyển mới</param>
        public void TriggerThayDoiTrangThaiDiChuyen(bool dangDiChuyen)
        {
            OnThayDoiTrangThaiDiChuyen?.Invoke(dangDiChuyen);
            LogEvent("ThayDoiTrangThaiDiChuyen", $"Trạng thái: {dangDiChuyen}");
        }

        /// <summary>
        /// Trigger event thay đổi vị trí
        /// </summary>
        /// <param name="viTriCu">Vị trí cũ</param>
        /// <param name="viTriMoi">Vị trí mới</param>
        public void TriggerThayDoiViTri(Vector3 viTriCu, Vector3 viTriMoi)
        {
            OnThayDoiViTri?.Invoke(viTriCu, viTriMoi);
            LogEvent("ThayDoiViTri", $"Từ {viTriCu} đến {viTriMoi}");
        }

        /// <summary>
        /// Trigger event thay đổi tốc độ
        /// </summary>
        /// <param name="tocDoCu">Tốc độ cũ</param>
        /// <param name="tocDoMoi">Tốc độ mới</param>
        public void TriggerThayDoiTocDo(float tocDoCu, float tocDoMoi)
        {
            OnThayDoiTocDo?.Invoke(tocDoCu, tocDoMoi);
            LogEvent("ThayDoiTocDo", $"Từ {tocDoCu:F2} đến {tocDoMoi:F2}");
        }

        /// <summary>
        /// Trigger event tương tác với đối tượng
        /// </summary>
        /// <param name="doiTuong">Đối tượng được tương tác</param>
        /// <param name="loaiTuongTac">Loại tương tác</param>
        public void TriggerTuongTacDoiTuong(GameObject doiTuong, string loaiTuongTac)
        {
            OnTuongTacDoiTuong?.Invoke(doiTuong, loaiTuongTac);
            LogEvent("TuongTacDoiTuong", $"Đối tượng: {doiTuong?.name}, Loại: {loaiTuongTac}");
        }

        /// <summary>
        /// Trigger event lỗi hệ thống
        /// </summary>
        /// <param name="thongDiepLoi">Thông điệp lỗi</param>
        public void TriggerLoiHeThong(string thongDiepLoi)
        {
            OnLoiHeThong?.Invoke(thongDiepLoi);
            LogEvent("LoiHeThong", thongDiepLoi);
            Debug.LogError($"[BorderEventSystem] Lỗi hệ thống: {thongDiepLoi}");
        }

        #endregion

        #region Dynamic Event System

        /// <summary>
        /// Đăng ký dynamic event mới
        /// </summary>
        /// <param name="tenEvent">Tên event</param>
        /// <returns>UnityEvent để subscribe</returns>
        public UnityEvent DangKyEvent(string tenEvent)
        {
            if (string.IsNullOrEmpty(tenEvent))
            {
                Debug.LogError("[BorderEventSystem] Tên event không được để trống!");
                return null;
            }

            if (!dynamicEvents.ContainsKey(tenEvent))
            {
                dynamicEvents[tenEvent] = new UnityEvent();
                LogEvent("DangKyEvent", $"Đã đăng ký event: {tenEvent}");
            }

            return dynamicEvents[tenEvent];
        }

        /// <summary>
        /// Đăng ký dynamic event với data
        /// </summary>
        /// <param name="tenEvent">Tên event</param>
        /// <returns>UnityEvent với data để subscribe</returns>
        public UnityEvent<object> DangKyEventVoiData(string tenEvent)
        {
            if (string.IsNullOrEmpty(tenEvent))
            {
                Debug.LogError("[BorderEventSystem] Tên event không được để trống!");
                return null;
            }

            if (!dynamicEventsWithData.ContainsKey(tenEvent))
            {
                dynamicEventsWithData[tenEvent] = new UnityEvent<object>();
                LogEvent("DangKyEventVoiData", $"Đã đăng ký event với data: {tenEvent}");
            }

            return dynamicEventsWithData[tenEvent];
        }

        /// <summary>
        /// Trigger dynamic event
        /// </summary>
        /// <param name="tenEvent">Tên event</param>
        public void TriggerEvent(string tenEvent)
        {
            if (dynamicEvents.ContainsKey(tenEvent))
            {
                dynamicEvents[tenEvent]?.Invoke();
                LogEvent("TriggerEvent", $"Đã trigger event: {tenEvent}");
            }
            else
            {
                Debug.LogWarning($"[BorderEventSystem] Event '{tenEvent}' không tồn tại!");
            }
        }

        /// <summary>
        /// Trigger dynamic event với data
        /// </summary>
        /// <param name="tenEvent">Tên event</param>
        /// <param name="data">Dữ liệu kèm theo</param>
        public void TriggerEventVoiData(string tenEvent, object data)
        {
            if (dynamicEventsWithData.ContainsKey(tenEvent))
            {
                dynamicEventsWithData[tenEvent]?.Invoke(data);
                LogEvent("TriggerEventVoiData", $"Đã trigger event với data: {tenEvent}");
            }
            else
            {
                Debug.LogWarning($"[BorderEventSystem] Event với data '{tenEvent}' không tồn tại!");
            }
        }

        /// <summary>
        /// Hủy đăng ký dynamic event
        /// </summary>
        /// <param name="tenEvent">Tên event</param>
        public void HuyDangKyEvent(string tenEvent)
        {
            if (dynamicEvents.ContainsKey(tenEvent))
            {
                dynamicEvents.Remove(tenEvent);
                LogEvent("HuyDangKyEvent", $"Đã hủy đăng ký event: {tenEvent}");
            }

            if (dynamicEventsWithData.ContainsKey(tenEvent))
            {
                dynamicEventsWithData.Remove(tenEvent);
                LogEvent("HuyDangKyEvent", $"Đã hủy đăng ký event với data: {tenEvent}");
            }
        }

        #endregion

        #region Event History

        /// <summary>
        /// Bật/tắt lưu lịch sử events
        /// </summary>
        /// <param name="batTat">True để bật, false để tắt</param>
        public void BatTatLuuLichSu(bool batTat)
        {
            luuLichSuEvents = batTat;
            Debug.Log($"[BorderEventSystem] Lưu lịch sử events: {(batTat ? "BẬT" : "TẮT")}");
        }

        /// <summary>
        /// Xóa lịch sử events
        /// </summary>
        public void XoaLichSu()
        {
            eventHistory.Clear();
            Debug.Log("[BorderEventSystem] Đã xóa lịch sử events");
        }

        /// <summary>
        /// Lấy lịch sử events
        /// </summary>
        /// <returns>Array chứa lịch sử events</returns>
        public EventHistoryEntry[] LayLichSu()
        {
            return eventHistory.ToArray();
        }

        /// <summary>
        /// Log event vào lịch sử
        /// </summary>
        /// <param name="tenEvent">Tên event</param>
        /// <param name="duLieu">Dữ liệu kèm theo</param>
        private void LogEvent(string tenEvent, string duLieu)
        {
            if (!luuLichSuEvents) return;

            var entry = new EventHistoryEntry
            {
                tenEvent = tenEvent,
                thoiGian = Time.time,
                duLieu = duLieu
            };

            eventHistory.Enqueue(entry);

            // Giới hạn số lượng entries
            while (eventHistory.Count > soLuongEventsToiDa)
            {
                eventHistory.Dequeue();
            }
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Lấy danh sách tất cả dynamic events
        /// </summary>
        /// <returns>Array chứa tên các events</returns>
        public string[] LayDanhSachEvents()
        {
            var allEvents = new List<string>();
            allEvents.AddRange(dynamicEvents.Keys);
            allEvents.AddRange(dynamicEventsWithData.Keys);
            return allEvents.ToArray();
        }

        /// <summary>
        /// Kiểm tra event có tồn tại không
        /// </summary>
        /// <param name="tenEvent">Tên event</param>
        /// <returns>True nếu event tồn tại</returns>
        public bool KiemTraEventTonTai(string tenEvent)
        {
            return dynamicEvents.ContainsKey(tenEvent) || dynamicEventsWithData.ContainsKey(tenEvent);
        }

        #endregion
    }
}
