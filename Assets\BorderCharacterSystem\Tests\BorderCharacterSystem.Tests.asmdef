{"name": "BorderCharacterSystem.Tests", "rootNamespace": "BorderCharacterSystem.Tests", "references": ["UnityEngine.TestRunner", "UnityEditor.TestRunner", "BorderCharacterSystem", "Unity.InputSystem", "Cinemachine"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": true, "precompiledReferences": ["nunit.framework.dll"], "autoReferenced": false, "defineConstraints": ["UNITY_INCLUDE_TESTS"], "versionDefines": [], "noEngineReferences": false}