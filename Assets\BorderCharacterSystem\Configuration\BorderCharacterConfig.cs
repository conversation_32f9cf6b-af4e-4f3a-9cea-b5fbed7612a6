using UnityEngine;
using BorderCharacterSystem.Configuration.Settings;

namespace BorderCharacterSystem.Configuration
{
    /// <summary>
    /// Cấu hình chính cho Border Character System
    /// Sử dụng ScriptableObject để dễ dàng tạo và quản lý presets
    /// 
    /// Tác giả: Border Character System Team
    /// Phiên bản: 1.0.0
    /// Ngày tạo: 2025-01-06
    /// </summary>
    [CreateAssetMenu(
        fileName = "BorderCharacterConfig", 
        menuName = "Border Character System/Cấu Hình Nhân Vật",
        order = 1
    )]
    public class BorderCharacterConfig : ScriptableObject
    {
        #region Metadata

        [Header("Thông Tin Cấu Hình")]
        [SerializeField] 
        [Tooltip("Tên của cấu hình này")]
        private string configName = "Cấu Hình Mặc Định";
        
        [SerializeField] 
        [TextArea(3, 5)]
        [Tooltip("<PERSON><PERSON> tả chi tiết về cấu hình này")]
        private string description = "Cấu hình mặc định cho Border Character System với các giá trị cân bằng cho hầu hết các game.";
        
        [SerializeField] 
        [Tooltip("Phiên bản của cấu hình")]
        private string version = "1.0.0";

        [SerializeField]
        [Tooltip("Tác giả tạo cấu hình này")]
        private string author = "Border Character System";

        #endregion

        #region Configuration Settings

        [Header("Cài Đặt Di Chuyển")]
        [Tooltip("Các thông số liên quan đến di chuyển của nhân vật")]
        public CaiDatDiChuyen movementSettings = new CaiDatDiChuyen();

        [Header("Cài Đặt Nhảy")]
        [Tooltip("Các thông số liên quan đến nhảy và trọng lực")]
        public CaiDatNhay jumpSettings = new CaiDatNhay();

        [Header("Cài Đặt Camera")]
        [Tooltip("Các thông số liên quan đến camera và góc nhìn")]
        public CaiDatCamera cameraSettings = new CaiDatCamera();

        [Header("Cài Đặt Tương Tác")]
        [Tooltip("Các thông số liên quan đến tương tác với môi trường")]
        public CaiDatTuongTac interactionSettings = new CaiDatTuongTac();

        [Header("Cài Đặt Debug")]
        [Tooltip("Các thông số liên quan đến debug và profiling")]
        public CaiDatDebug debugSettings = new CaiDatDebug();

        [Header("Cài Đặt Localization")]
        [Tooltip("Các thông số liên quan đến đa ngôn ngữ")]
        public CaiDatLocalization localizationSettings = new CaiDatLocalization();

        #endregion

        #region Properties

        /// <summary>Tên của cấu hình</summary>
        public string ConfigName => configName;
        
        /// <summary>Mô tả của cấu hình</summary>
        public string Description => description;
        
        /// <summary>Phiên bản của cấu hình</summary>
        public string Version => version;
        
        /// <summary>Tác giả của cấu hình</summary>
        public string Author => author;

        #endregion

        #region Public Methods

        /// <summary>
        /// Tạo bản sao của cấu hình hiện tại
        /// </summary>
        /// <returns>Bản sao của cấu hình</returns>
        public BorderCharacterConfig Clone()
        {
            var clone = CreateInstance<BorderCharacterConfig>();
            
            // Copy metadata
            clone.configName = configName + " (Bản Sao)";
            clone.description = description;
            clone.version = version;
            clone.author = author;
            
            // Copy settings
            clone.movementSettings = movementSettings.Clone();
            clone.jumpSettings = jumpSettings.Clone();
            clone.cameraSettings = cameraSettings.Clone();
            clone.interactionSettings = interactionSettings.Clone();
            clone.debugSettings = debugSettings.Clone();
            clone.localizationSettings = localizationSettings.Clone();
            
            return clone;
        }

        /// <summary>
        /// Validate cấu hình và hiển thị warnings nếu cần
        /// </summary>
        public void ValidateConfiguration()
        {
            bool hasErrors = false;

            // Validate each settings group
            if (!movementSettings.Validate())
            {
                Debug.LogWarning($"[BorderCharacterConfig] Cài đặt di chuyển có vấn đề trong config '{configName}'");
                hasErrors = true;
            }

            if (!jumpSettings.Validate())
            {
                Debug.LogWarning($"[BorderCharacterConfig] Cài đặt nhảy có vấn đề trong config '{configName}'");
                hasErrors = true;
            }

            if (!cameraSettings.Validate())
            {
                Debug.LogWarning($"[BorderCharacterConfig] Cài đặt camera có vấn đề trong config '{configName}'");
                hasErrors = true;
            }

            if (!interactionSettings.Validate())
            {
                Debug.LogWarning($"[BorderCharacterConfig] Cài đặt tương tác có vấn đề trong config '{configName}'");
                hasErrors = true;
            }

            if (!debugSettings.Validate())
            {
                Debug.LogWarning($"[BorderCharacterConfig] Cài đặt debug có vấn đề trong config '{configName}'");
                hasErrors = true;
            }

            if (!localizationSettings.Validate())
            {
                Debug.LogWarning($"[BorderCharacterConfig] Cài đặt localization có vấn đề trong config '{configName}'");
                hasErrors = true;
            }

            if (!hasErrors)
            {
                Debug.Log($"[BorderCharacterConfig] Cấu hình '{configName}' hợp lệ!");
            }
        }

        /// <summary>
        /// Reset về giá trị mặc định
        /// </summary>
        public void ResetToDefaults()
        {
            movementSettings = new CaiDatDiChuyen();
            jumpSettings = new CaiDatNhay();
            cameraSettings = new CaiDatCamera();
            interactionSettings = new CaiDatTuongTac();
            debugSettings = new CaiDatDebug();
            localizationSettings = new CaiDatLocalization();

            Debug.Log($"[BorderCharacterConfig] Đã reset cấu hình '{configName}' về giá trị mặc định");
        }

        /// <summary>
        /// Xuất cấu hình ra JSON string
        /// </summary>
        /// <returns>JSON string của cấu hình</returns>
        public string ExportToJson()
        {
            var exportData = new ConfigExportData
            {
                configName = this.configName,
                description = this.description,
                version = this.version,
                author = this.author,
                movementSettings = this.movementSettings,
                jumpSettings = this.jumpSettings,
                cameraSettings = this.cameraSettings,
                interactionSettings = this.interactionSettings,
                debugSettings = this.debugSettings,
                localizationSettings = this.localizationSettings
            };

            return JsonUtility.ToJson(exportData, true);
        }

        /// <summary>
        /// Import cấu hình từ JSON string
        /// </summary>
        /// <param name="jsonData">JSON string chứa dữ liệu cấu hình</param>
        /// <returns>True nếu import thành công</returns>
        public bool ImportFromJson(string jsonData)
        {
            try
            {
                var importData = JsonUtility.FromJson<ConfigExportData>(jsonData);
                
                configName = importData.configName;
                description = importData.description;
                version = importData.version;
                author = importData.author;
                movementSettings = importData.movementSettings;
                jumpSettings = importData.jumpSettings;
                cameraSettings = importData.cameraSettings;
                interactionSettings = importData.interactionSettings;
                debugSettings = importData.debugSettings;
                localizationSettings = importData.localizationSettings;

                ValidateConfiguration();
                
                Debug.Log($"[BorderCharacterConfig] Đã import thành công cấu hình '{configName}'");
                return true;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[BorderCharacterConfig] Lỗi khi import cấu hình: {e.Message}");
                return false;
            }
        }

        #endregion

        #region Unity Events

        private void OnValidate()
        {
            // Validate configuration khi thay đổi trong Inspector
            ValidateConfiguration();
        }

        private void OnEnable()
        {
            // Ensure default values are set
            if (string.IsNullOrEmpty(configName))
                configName = "Cấu Hình Mặc Định";
        }

        #endregion

        #region Helper Classes

        /// <summary>
        /// Class helper để export/import JSON
        /// </summary>
        [System.Serializable]
        private class ConfigExportData
        {
            public string configName;
            public string description;
            public string version;
            public string author;
            public CaiDatDiChuyen movementSettings;
            public CaiDatNhay jumpSettings;
            public CaiDatCamera cameraSettings;
            public CaiDatTuongTac interactionSettings;
            public CaiDatDebug debugSettings;
            public CaiDatLocalization localizationSettings;
        }

        #endregion
    }
}
