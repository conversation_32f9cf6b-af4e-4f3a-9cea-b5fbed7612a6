using UnityEngine;
using UnityEngine.UI;
using TMPro;
using BorderCharacterSystem.Core;
using BorderCharacterSystem.Configuration;

namespace BorderCharacterSystem.Examples
{
    /// <summary>
    /// Script test và demo cho Border Character System
    /// Hiển thị thông tin real-time và cho phép test các tính năng
    /// </summary>
    public class BorderCharacterTester : MonoBehaviour
    {
        #region References

        [Header("Character System")]
        [SerializeField]
        [Toolt<PERSON>("Border Character Controller cần test")]
        private BorderCharacterController characterController;

        [Header("UI References")]
        [SerializeField]
        [Tooltip("Text hiển thị thông tin trạng thái")]
        private TextMeshProUGUI statusText;

        [SerializeField]
        [Tooltip("Text hiển thị thông tin input")]
        private TextMeshProUGUI inputText;

        [SerializeField]
        [Tooltip("Text hiển thị event log")]
        private TextMeshProUGUI eventLogText;

        [SerializeField]
        [Tooltip("Button toggle debug mode")]
        private Button debugToggleButton;

        [SerializeField]
        [Tooltip("Dropdown chọn config")]
        private TMP_Dropdown configDropdown;

        [Header("Test Configurations")]
        [SerializeField]
        [Tooltip("Danh sách configs để test")]
        private BorderCharacterConfig[] testConfigs;

        #endregion

        #region Private Fields

        private string eventLog = "";
        private int maxLogEntries = 10;
        private bool debugMode = false;

        #endregion

        #region Unity Lifecycle

        private void Start()
        {
            InitializeTester();
            SetupUI();
            RegisterEvents();
        }

        private void Update()
        {
            UpdateStatusDisplay();
            UpdateInputDisplay();
            HandleTestInputs();
        }

        private void OnDestroy()
        {
            UnregisterEvents();
        }

        #endregion

        #region Initialization

        /// <summary>
        /// Khởi tạo tester
        /// </summary>
        private void InitializeTester()
        {
            // Tìm character controller nếu chưa assign
            if (characterController == null)
            {
                characterController = FindObjectOfType<BorderCharacterController>();
                
                if (characterController == null)
                {
                    Debug.LogError("[BorderCharacterTester] Không tìm thấy BorderCharacterController trong scene!");
                    enabled = false;
                    return;
                }
            }

            // Validate test configs
            if (testConfigs == null || testConfigs.Length == 0)
            {
                Debug.LogWarning("[BorderCharacterTester] Không có test configs. Chỉ sử dụng config hiện tại.");
            }

            Debug.Log("[BorderCharacterTester] Đã khởi tạo thành công");
        }

        /// <summary>
        /// Setup UI elements
        /// </summary>
        private void SetupUI()
        {
            // Setup debug toggle button
            if (debugToggleButton != null)
            {
                debugToggleButton.onClick.AddListener(ToggleDebugMode);
                UpdateDebugButtonText();
            }

            // Setup config dropdown
            if (configDropdown != null && testConfigs != null && testConfigs.Length > 0)
            {
                configDropdown.ClearOptions();
                
                var options = new System.Collections.Generic.List<string>();
                options.Add("Config Hiện Tại");
                
                foreach (var config in testConfigs)
                {
                    if (config != null)
                        options.Add(config.ConfigName);
                }
                
                configDropdown.AddOptions(options);
                configDropdown.onValueChanged.AddListener(OnConfigChanged);
            }

            // Initialize text displays
            if (statusText != null)
                statusText.text = "Đang khởi tạo...";
            
            if (inputText != null)
                inputText.text = "Input: Chưa có";
            
            if (eventLogText != null)
                eventLogText.text = "Event Log:\n";
        }

        #endregion

        #region Event Registration

        /// <summary>
        /// Đăng ký events từ character controller
        /// </summary>
        private void RegisterEvents()
        {
            if (characterController == null) return;

            // Movement events
            characterController.OnBatDauDiChuyen.AddListener(() => LogEvent("Bắt đầu di chuyển"));
            characterController.OnKetThucDiChuyen.AddListener(() => LogEvent("Kết thúc di chuyển"));
            characterController.OnBatDauNhay.AddListener(() => LogEvent("Bắt đầu nhảy"));
            characterController.OnHaCanh.AddListener(() => LogEvent("Hạ cánh"));
            characterController.OnThayDoiTrangThaiCui.AddListener((crouching) => 
                LogEvent($"Thay đổi trạng thái cúi: {(crouching ? "Cúi" : "Đứng")}"));

            // Input events
            if (characterController.InputManager != null)
            {
                characterController.InputManager.OnNhanNutNhay.AddListener(() => LogEvent("Nhấn nút nhảy"));
                characterController.InputManager.OnNhanNutTuongTac.AddListener(() => LogEvent("Nhấn nút tương tác"));
            }

            Debug.Log("[BorderCharacterTester] Đã đăng ký tất cả events");
        }

        /// <summary>
        /// Hủy đăng ký events
        /// </summary>
        private void UnregisterEvents()
        {
            if (characterController == null) return;

            characterController.OnBatDauDiChuyen.RemoveAllListeners();
            characterController.OnKetThucDiChuyen.RemoveAllListeners();
            characterController.OnBatDauNhay.RemoveAllListeners();
            characterController.OnHaCanh.RemoveAllListeners();
            characterController.OnThayDoiTrangThaiCui.RemoveAllListeners();

            if (characterController.InputManager != null)
            {
                characterController.InputManager.OnNhanNutNhay.RemoveAllListeners();
                characterController.InputManager.OnNhanNutTuongTac.RemoveAllListeners();
            }
        }

        #endregion

        #region UI Updates

        /// <summary>
        /// Cập nhật hiển thị trạng thái
        /// </summary>
        private void UpdateStatusDisplay()
        {
            if (statusText == null || characterController == null) return;

            var trangThai = characterController.LayThongTinTrangThai();
            
            string statusInfo = $"=== TRẠNG THÁI NHÂN VẬT ===\n" +
                               $"Đang đứng trên đất: {(trangThai.dangDungTrenDat ? "✓" : "✗")}\n" +
                               $"Đang di chuyển: {(trangThai.dangDiChuyen ? "✓" : "✗")}\n" +
                               $"Đang nhảy: {(trangThai.dangNhay ? "✓" : "✗")}\n" +
                               $"Đang cúi: {(trangThai.dangCui ? "✓" : "✗")}\n" +
                               $"Đang chạm tường: {(trangThai.dangChamTuong ? "✓" : "✗")}\n\n" +
                               $"=== THÔNG TIN VẬT LÝ ===\n" +
                               $"Vị trí: {trangThai.viTri.ToString("F2")}\n" +
                               $"Vận tốc: {trangThai.vanToc.magnitude:F2} m/s\n" +
                               $"Hướng: {trangThai.vanToc.normalized.ToString("F2")}\n\n" +
                               $"=== HIỆU SUẤT ===\n" +
                               $"FPS: {(1f / Time.deltaTime):F1}\n" +
                               $"Delta Time: {Time.deltaTime * 1000:F1}ms";

            statusText.text = statusInfo;
        }

        /// <summary>
        /// Cập nhật hiển thị input
        /// </summary>
        private void UpdateInputDisplay()
        {
            if (inputText == null || characterController?.InputManager == null) return;

            var inputManager = characterController.InputManager;
            
            string inputInfo = $"=== INPUT HIỆN TẠI ===\n" +
                              $"Di chuyển: {inputManager.GiaTriDiChuyen.ToString("F2")}\n" +
                              $"Nhìn: {inputManager.GiaTriNhin.ToString("F2")}\n" +
                              $"Nhảy: {(inputManager.DangNhanNutNhay ? "✓" : "✗")}\n" +
                              $"Cúi: {(inputManager.DangNhanNutCui ? "✓" : "✗")}\n" +
                              $"Chạy: {(inputManager.DangNhanNutChay ? "✓" : "✗")}\n" +
                              $"Tương tác: {(inputManager.DangNhanNutTuongTac ? "✓" : "✗")}\n\n" +
                              $"=== TRẠNG THÁI INPUT ===\n" +
                              $"Có input di chuyển: {(inputManager.CoInputDiChuyen ? "✓" : "✗")}\n" +
                              $"Có input nhìn: {(inputManager.CoInputNhin ? "✓" : "✗")}";

            inputText.text = inputInfo;
        }

        #endregion

        #region Event Logging

        /// <summary>
        /// Log event vào display
        /// </summary>
        /// <param name="eventMessage">Thông điệp event</param>
        private void LogEvent(string eventMessage)
        {
            string timestamp = System.DateTime.Now.ToString("HH:mm:ss");
            string logEntry = $"[{timestamp}] {eventMessage}";
            
            // Add to log
            eventLog = logEntry + "\n" + eventLog;
            
            // Limit log entries
            var lines = eventLog.Split('\n');
            if (lines.Length > maxLogEntries)
            {
                eventLog = string.Join("\n", lines, 0, maxLogEntries);
            }
            
            // Update display
            if (eventLogText != null)
            {
                eventLogText.text = "=== EVENT LOG ===\n" + eventLog;
            }
            
            // Debug log if enabled
            if (debugMode)
            {
                Debug.Log($"[BorderCharacterTester] {logEntry}");
            }
        }

        #endregion

        #region Test Controls

        /// <summary>
        /// Xử lý test inputs
        /// </summary>
        private void HandleTestInputs()
        {
            // Toggle debug với F1
            if (Input.GetKeyDown(KeyCode.F1))
            {
                ToggleDebugMode();
            }
            
            // Clear event log với F2
            if (Input.GetKeyDown(KeyCode.F2))
            {
                ClearEventLog();
            }
            
            // Test config switching với số
            if (testConfigs != null)
            {
                for (int i = 0; i < testConfigs.Length && i < 9; i++)
                {
                    if (Input.GetKeyDown(KeyCode.Alpha1 + i))
                    {
                        SwitchToConfig(i);
                    }
                }
            }
        }

        /// <summary>
        /// Toggle debug mode
        /// </summary>
        private void ToggleDebugMode()
        {
            debugMode = !debugMode;
            
            if (characterController != null)
            {
                characterController.BatTatCheDoDebug(debugMode);
            }
            
            UpdateDebugButtonText();
            LogEvent($"Debug mode: {(debugMode ? "BẬT" : "TẮT")}");
        }

        /// <summary>
        /// Cập nhật text của debug button
        /// </summary>
        private void UpdateDebugButtonText()
        {
            if (debugToggleButton != null)
            {
                var buttonText = debugToggleButton.GetComponentInChildren<TextMeshProUGUI>();
                if (buttonText != null)
                {
                    buttonText.text = debugMode ? "Tắt Debug" : "Bật Debug";
                }
            }
        }

        /// <summary>
        /// Clear event log
        /// </summary>
        private void ClearEventLog()
        {
            eventLog = "";
            if (eventLogText != null)
            {
                eventLogText.text = "=== EVENT LOG ===\n(Đã xóa)";
            }
            LogEvent("Đã xóa event log");
        }

        /// <summary>
        /// Switch to specific config
        /// </summary>
        /// <param name="configIndex">Index của config</param>
        private void SwitchToConfig(int configIndex)
        {
            if (testConfigs == null || configIndex >= testConfigs.Length || testConfigs[configIndex] == null)
            {
                LogEvent($"Config {configIndex + 1} không hợp lệ");
                return;
            }

            var config = testConfigs[configIndex];
            characterController.ThietLapCauHinh(config);
            LogEvent($"Chuyển sang config: {config.ConfigName}");
            
            // Update dropdown
            if (configDropdown != null)
            {
                configDropdown.value = configIndex + 1; // +1 vì có "Config Hiện Tại" ở đầu
            }
        }

        #endregion

        #region UI Callbacks

        /// <summary>
        /// Callback khi thay đổi config dropdown
        /// </summary>
        /// <param name="index">Index được chọn</param>
        private void OnConfigChanged(int index)
        {
            if (index == 0) return; // "Config Hiện Tại"
            
            int configIndex = index - 1;
            SwitchToConfig(configIndex);
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Set character controller reference (for external setup)
        /// </summary>
        /// <param name="controller">Character controller</param>
        public void SetCharacterController(BorderCharacterController controller)
        {
            if (characterController != null)
            {
                UnregisterEvents();
            }
            
            characterController = controller;
            
            if (characterController != null)
            {
                RegisterEvents();
                LogEvent($"Đã set character controller: {characterController.name}");
            }
        }

        /// <summary>
        /// Add test config
        /// </summary>
        /// <param name="config">Config to add</param>
        public void AddTestConfig(BorderCharacterConfig config)
        {
            if (config == null) return;
            
            var newConfigs = new BorderCharacterConfig[testConfigs.Length + 1];
            testConfigs.CopyTo(newConfigs, 0);
            newConfigs[testConfigs.Length] = config;
            testConfigs = newConfigs;
            
            // Refresh dropdown
            SetupUI();
            
            LogEvent($"Đã thêm test config: {config.ConfigName}");
        }

        #endregion
    }
}
