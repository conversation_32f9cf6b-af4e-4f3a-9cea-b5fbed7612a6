# Kế Hoạch Phát Triển Hệ Thống Character Controller Tùy Chỉnh
## Dựa Trên Nappin PhysicsCharacterController

**Dự Án:** Border Documentary Character System
**Mục Tiêu:** X<PERSON><PERSON> dựng hệ thống character controller t<PERSON><PERSON> sử dụng, dễ sử dụng
**Engine:** Unity 6000.0.50f1
**Render Pipeline:** Universal Render Pipeline (URP)
**Ngôn <PERSON>:** C# với Unity Input System
**Hỗ Trợ Ngôn Ngữ:** Ti<PERSON><PERSON> (chính), Ti<PERSON><PERSON> (phụ)

## Phân Tích Asset Nappin PhysicsCharacterController

### Điểm Mạnh Đã Xác Định:
- ✅ Physics-based movement với Rigidbody
- ✅ Hỗ trợ đầy đủ: walk, run, jump, crouch, wall interaction
- ✅ Kiến trúc modular: tách biệt input, movement, animation
- ✅ Camera system: first-person và third-person
- ✅ Platform support: moving platforms, slopes, stairs, ladders
- ✅ Animation integration với Animator Controller
- ✅ Extension framework có sẵn

### Hạn Chế Cần Cải Thiện:
- ❌ Thiếu documentation tiếng Việt và inline comments
- ❌ Không hỗ trợ localization system
- ❌ Event system cơ bản, cần mở rộng
- ❌ Thiếu ScriptableObject configuration
- ❌ Không có unit tests
- ❌ Code organization: một số class quá lớn
- ❌ Thiếu wrapper layer dễ sử dụng

## 1. Hệ Thống Gameplay Cốt Lõi

### 1.1 Hệ Thống Nhân Vật (Ưu Tiên CAO)
- **CharacterManager** (Mới) - Điều khiển vật lý nhân vật
- **DocumentaryCharacterController** (Mới) - Mở rộng cho gameplay documentary
- **CharacterInteractionSystem** (Mới) - Tương tác với môi trường
- **CharacterInventorySystem** (Mới) - Quản lý vật phẩm thu thập

**Phụ thuộc:** Nappin PhysicsCharacterController (đã có)  
**Thời gian ước tính:** 2-3 tuần

### 1.2 Hệ Thống Camera và Quan Sát (Ưu Tiên CAO)
- **DocumentaryCameraController** (Mới) - Camera chế độ documentary
- **PhotoModeSystem** (Mới) - Chụp ảnh trong game
- **CinematicCameraSystem** (Mới) - Camera cho cutscenes
- **CameraTransitionManager** (Mở rộng từ CameraManager có sẵn)

**Phụ thuộc:** Cinemachine 3.1.3 (đã có), CameraManager (đã có)  
**Thời gian ước tính:** 2 tuần

### 1.3 Hệ Thống Tương Tác và Khám Phá (Ưu Tiên CAO)
- **InteractableObjectSystem** (Mới) - Đối tượng có thể tương tác
- **DocumentaryClueSystem** (Mới) - Thu thập manh mối
- **EnvironmentNarrativeSystem** (Mới) - Kể chuyện qua môi trường
- **ExplorationProgressTracker** (Mới) - Theo dõi tiến độ khám phá

**Phụ thuộc:** CharacterManager, Input System  
**Thời gian ước tính:** 3 tuần

## 2. Kiến Trúc Code và Cấu Trúc

### 2.1 Cấu Trúc Thư Mục Đề Xuất
```
Assets/
├── BoderDocumentary/
│   ├── Scripts/
│   │   ├── Core/
│   │   │   ├── GameManager.cs
│   │   │   ├── SceneManager.cs
│   │   │   └── SaveSystem.cs
│   │   ├── Character/
│   │   │   ├── DocumentaryCharacterController.cs
│   │   │   ├── CharacterInteractionSystem.cs
│   │   │   └── CharacterInventorySystem.cs
│   │   ├── Camera/
│   │   │   ├── DocumentaryCameraController.cs
│   │   │   ├── PhotoModeSystem.cs
│   │   │   └── CinematicCameraSystem.cs
│   │   ├── Interaction/
│   │   │   ├── InteractableObjectSystem.cs
│   │   │   ├── DocumentaryClueSystem.cs
│   │   │   └── EnvironmentNarrativeSystem.cs
│   │   ├── UI/
│   │   │   ├── DocumentaryUI.cs
│   │   │   ├── InventoryUI.cs
│   │   │   └── PhotoGalleryUI.cs
│   │   └── Audio/
│   │       ├── AudioManager.cs
│   │       ├── NarrativeAudioSystem.cs
│   │       └── EnvironmentAudioSystem.cs
│   ├── Prefabs/
│   ├── Materials/
│   ├── Scenes/
│   └── Resources/
```

### 2.2 Patterns và Kiến Trúc
- **Singleton Pattern:** GameManager, AudioManager
- **Observer Pattern:** Event system cho tương tác
- **State Machine:** Character states, Game states
- **Component-based Architecture:** Tận dụng Unity ECS concepts
- **ScriptableObject:** Configuration data, narrative content

## 3. Mức Độ Ưu Tiên Các Component

### Ưu Tiên CAO (Prototype Phase)
1. **GameManager** - Quản lý trạng thái game tổng thể
2. **DocumentaryCharacterController** - Mở rộng character controller
3. **InteractableObjectSystem** - Tương tác cơ bản
4. **DocumentaryUI** - UI cơ bản cho prototype
5. **SaveSystem** - Lưu/tải game cơ bản

### Ưu Tiên TRUNG BÌNH
1. **PhotoModeSystem** - Chụp ảnh trong game
2. **DocumentaryClueSystem** - Thu thập manh mối
3. **AudioManager** - Quản lý âm thanh
4. **InventoryUI** - Giao diện inventory
5. **CinematicCameraSystem** - Camera cutscenes

### Ưu Tiên THẤP (Post-Prototype)
1. **EnvironmentNarrativeSystem** - Kể chuyện nâng cao
2. **PhotoGalleryUI** - Thư viện ảnh
3. **NarrativeAudioSystem** - Âm thanh narrative phức tạp
4. **ExplorationProgressTracker** - Tracking nâng cao
5. **EnvironmentAudioSystem** - Âm thanh môi trường động

## 4. Phụ Thuộc Giữa Các Module

### Core Dependencies
```
GameManager
├── SceneManager
├── SaveSystem
└── AudioManager

CharacterSystem
├── DocumentaryCharacterController
│   ├── CharacterManager (existing)
│   ├── InputReader (existing)
│   └── CharacterInteractionSystem
├── CharacterInventorySystem
└── CameraManager (existing)

InteractionSystem
├── InteractableObjectSystem
├── DocumentaryClueSystem
└── EnvironmentNarrativeSystem
    └── AudioManager

UISystem
├── DocumentaryUI
├── InventoryUI
└── PhotoGalleryUI
    └── PhotoModeSystem
```

## 5. Timeline Phát Triển Prototype

### Tuần 1-2: Foundation Setup
- [ ] Tạo cấu trúc thư mục dự án
- [ ] Implement GameManager cơ bản
- [ ] Setup SaveSystem cơ bản
- [ ] Tạo DocumentaryCharacterController base

### Tuần 3-4: Core Interaction
- [ ] Implement InteractableObjectSystem
- [ ] Tạo DocumentaryUI cơ bản
- [ ] Setup Input System cho tương tác
- [ ] Test tương tác cơ bản

### Tuần 5-6: Camera và Visual
- [ ] Implement PhotoModeSystem
- [ ] Mở rộng CameraManager cho documentary
- [ ] Tạo UI cho photo mode
- [ ] Test camera transitions

### Tuần 7-8: Content và Polish
- [ ] Implement DocumentaryClueSystem
- [ ] Tạo InventoryUI
- [ ] Setup AudioManager cơ bản
- [ ] Integration testing và bug fixes

## 6. Yêu Cầu Kỹ Thuật và Ràng Buộc

### Yêu Cầu Hệ Thống
- **Unity Version:** 6000.0.50f1 trở lên
- **Render Pipeline:** URP 17.0.4
- **Input System:** Unity Input System 1.14.0
- **Camera System:** Cinemachine 3.1.3
- **Platform Target:** PC (Windows) cho prototype

### Ràng Buộc Kỹ Thuật
- Tương thích với Nappin PhysicsCharacterController hiện có
- Sử dụng Unity Input System (không legacy input)
- Tuân thủ URP rendering pipeline
- Component-based architecture cho modularity
- Performance target: 60 FPS trên mid-range PC

### Packages Cần Thiết (Đã có sẵn)
- ✅ Unity Input System (1.14.0)
- ✅ Cinemachine (3.1.3)
- ✅ Universal Render Pipeline (17.0.4)
- ✅ Visual Effect Graph (17.0.4)
- ✅ Timeline (1.8.7)

## 7. Tích Hợp với Hệ Thống Hiện Có

### Nappin PhysicsCharacterController Integration
- **Mở rộng CharacterManager** thay vì thay thế
- **Sử dụng InputReader** hiện có làm base
- **Tận dụng CameraManager** cho documentary cameras
- **Giữ nguyên animation system** hiện có

### Existing Assets Utilization
- Sử dụng prefabs và materials có sẵn
- Mở rộng VFX system cho documentary effects
- Tận dụng animation controllers hiện có
- Sử dụng example scenes làm base cho prototype scenes

## 8. Kế Hoạch Testing và QA

### Unit Testing
- Test individual components
- Mock dependencies cho isolated testing
- Automated testing cho core systems

### Integration Testing
- Character movement với interaction system
- Camera transitions và state management
- Save/Load functionality
- UI responsiveness

### Performance Testing
- Frame rate monitoring
- Memory usage optimization
- Loading time optimization
- Input responsiveness testing

## 9. Gameplay Mechanics Chi Tiết

### 9.1 Documentary Investigation System
- **Evidence Collection:** Thu thập tài liệu, ảnh, ghi âm
- **Timeline Reconstruction:** Xây dựng lại dòng thời gian sự kiện
- **Character Interviews:** Tương tác với NPCs để thu thập thông tin
- **Location Analysis:** Phân tích môi trường để tìm manh mối

### 9.2 Narrative Progression System
- **Story Branching:** Câu chuyện thay đổi dựa trên lựa chọn người chơi
- **Multiple Perspectives:** Xem sự kiện từ nhiều góc độ khác nhau
- **Truth Revelation:** Dần dần khám phá sự thật qua gameplay
- **Emotional Impact:** Hệ thống đánh giá tác động cảm xúc

### 9.3 Environmental Storytelling
- **Visual Clues:** Manh mối kể chuyện qua hình ảnh
- **Audio Logs:** Ghi âm ẩn trong môi trường
- **Interactive Objects:** Đồ vật kể chuyện khi tương tác
- **Atmospheric Changes:** Môi trường thay đổi theo tiến độ câu chuyện

## 10. Technical Implementation Details

### 10.1 Data Management
```csharp
// ScriptableObject cho Documentary Data
[CreateAssetMenu(fileName = "DocumentaryData", menuName = "Boder/Documentary Data")]
public class DocumentaryData : ScriptableObject
{
    public string documentaryTitle;
    public List<ClueData> clues;
    public List<CharacterData> characters;
    public List<LocationData> locations;
    public TimelineData timeline;
}
```

### 10.2 Event System Architecture
```csharp
// Event system cho documentary interactions
public static class DocumentaryEvents
{
    public static UnityAction<ClueData> OnClueDiscovered;
    public static UnityAction<CharacterData> OnCharacterMet;
    public static UnityAction<LocationData> OnLocationExplored;
    public static UnityAction<float> OnProgressUpdated;
}
```

### 10.3 State Management
- **Game States:** MainMenu, Investigation, Interview, Review, Conclusion
- **Character States:** Idle, Walking, Investigating, Interacting, Photographing
- **Investigation States:** Searching, Analyzing, Documenting, Reflecting

## 11. Asset Requirements và Resource Planning

### 11.1 Art Assets Cần Thiết
- **Character Models:** Protagonist, NPCs (5-7 characters)
- **Environment Assets:** Investigation locations (3-5 scenes)
- **Props:** Documents, photos, evidence objects
- **UI Elements:** Documentary-style interface components

### 11.2 Audio Assets
- **Voice Acting:** Character dialogues và narration
- **Ambient Audio:** Environmental sounds cho từng location
- **Music:** Atmospheric soundtrack cho documentary mood
- **Sound Effects:** Interaction sounds, UI feedback

### 11.3 Narrative Content
- **Script Writing:** Dialogue trees và narrative text
- **Documentary Materials:** Fake documents, photos, recordings
- **Character Backstories:** Detailed character profiles
- **Timeline Events:** Chronological event database

## 12. Performance Optimization Strategy

### 12.1 Rendering Optimization
- **LOD System:** Level of detail cho complex models
- **Occlusion Culling:** Ẩn objects không nhìn thấy
- **Texture Streaming:** Dynamic texture loading
- **Lighting Optimization:** Baked lighting cho static objects

### 12.2 Memory Management
- **Object Pooling:** Reuse objects thay vì destroy/instantiate
- **Asset Bundling:** Efficient asset loading và unloading
- **Garbage Collection:** Minimize allocations trong runtime
- **Texture Compression:** Optimize texture memory usage

### 12.3 Loading Optimization
- **Async Loading:** Non-blocking scene transitions
- **Progressive Loading:** Load content as needed
- **Preloading Strategy:** Smart preloading của critical assets
- **Streaming Assets:** Stream large audio/video files

## 13. Quality Assurance và Testing Strategy

### 13.1 Functional Testing
- **Feature Testing:** Test từng feature riêng biệt
- **Integration Testing:** Test tương tác giữa systems
- **Regression Testing:** Ensure updates không break existing features
- **User Acceptance Testing:** Test với target audience

### 13.2 Performance Testing
- **Frame Rate Testing:** Maintain 60 FPS target
- **Memory Profiling:** Monitor memory usage patterns
- **Loading Time Testing:** Optimize scene transition times
- **Input Lag Testing:** Ensure responsive controls

### 13.3 Platform Testing
- **Hardware Compatibility:** Test trên different PC configurations
- **Resolution Testing:** Support multiple screen resolutions
- **Input Device Testing:** Keyboard/mouse và gamepad support
- **OS Compatibility:** Windows 10/11 compatibility

## 14. Risk Management và Mitigation

### 14.1 Technical Risks
- **Performance Issues:** Mitigation through early profiling
- **Integration Conflicts:** Regular integration testing
- **Asset Pipeline Issues:** Standardized asset workflows
- **Platform Compatibility:** Early platform testing

### 14.2 Design Risks
- **Narrative Complexity:** Iterative story development
- **User Experience Issues:** Regular playtesting
- **Scope Creep:** Strict feature prioritization
- **Technical Debt:** Code review và refactoring schedule

### 14.3 Timeline Risks
- **Feature Delays:** Buffer time trong schedule
- **Resource Availability:** Backup plans cho critical resources
- **External Dependencies:** Minimize external dependencies
- **Quality Issues:** Continuous testing throughout development

---

**Kết Luận:** Kế hoạch này cung cấp roadmap chi tiết cho việc phát triển prototype game "Boder Documentary". Việc thực hiện sẽ được chia thành các sprint ngắn với regular reviews và adjustments dựa trên progress và feedback. Mục tiêu là tạo ra một prototype functional và engaging trong 8 tuần đầu tiên, sau đó iterate dựa trên testing results và user feedback.

**Next Steps:**
1. Review và approve kế hoạch này
2. Setup development environment và project structure
3. Begin implementation theo timeline đã định
4. Establish regular review meetings và progress tracking
5. Prepare cho first milestone demo sau 2 tuần
