using UnityEngine;

namespace BorderCharacterSystem.Configuration.Settings
{
    /// <summary>
    /// Cài đặt liên quan đến di chuyển của nhân vật
    /// Bao gồm tốc độ, gia tốc, ma sát và các thông số vật lý khác
    /// </summary>
    [System.Serializable]
    public class CaiDatDiChuyen
    {
        #region Tốc Độ

        [Header("Tốc Độ Di Chuyển")]
        [Range(1f, 20f)]
        [Tooltip("Tốc độ đi bộ bình thường của nhân vật (m/s)")]
        public float tocDoDiBo = 5f;
        
        [Range(5f, 30f)]
        [Tooltip("Tốc độ chạy nhanh của nhân vật (m/s)")]
        public float tocDoChay = 10f;
        
        [Range(0.1f, 1f)]
        [Tooltip("Hệ số giảm tốc độ khi nhân vật đang cúi (0.1 = 10% tốc độ gốc)")]
        public float heSoTocDoKhiCui = 0.5f;
        
        [Range(0.01f, 0.99f)]
        [Tooltip("Ngưỡng input tối thiểu để kích hoạt di chuyển")]
        public float nguongInputToiThieu = 0.01f;

        #endregion

        #region Gia Tốc và Giảm Tốc

        [Header("Gia Tốc và Giảm Tốc")]
        [Range(0.01f, 1f)]
        [Tooltip("Thời gian để đạt tốc độ tối đa (giây) - càng nhỏ càng nhanh")]
        public float thoiGianTangToc = 0.2f;
        
        [Range(0.01f, 1f)]
        [Tooltip("Thời gian để dừng hoàn toàn (giây) - càng nhỏ càng nhanh")]
        public float thoiGianGiamToc = 0.1f;
        
        [Range(0.1f, 2f)]
        [Tooltip("Hệ số gia tốc khi bắt đầu di chuyển")]
        public float heSoGiaToc = 1f;
        
        [Range(0.1f, 2f)]
        [Tooltip("Hệ số giảm tốc khi dừng di chuyển")]
        public float heSoGiamToc = 1f;

        #endregion

        #region Ma Sát

        [Header("Ma Sát")]
        [Range(0f, 1f)]
        [Tooltip("Ma sát với mặt đất - ảnh hưởng đến độ trơn trượt")]
        public float maSatMatDat = 0.3f;
        
        [Range(0f, 1f)]
        [Tooltip("Ma sát với tường - ảnh hưởng đến khả năng leo tường")]
        public float maSatTuong = 0.8f;
        
        [Range(0f, 1f)]
        [Tooltip("Ma sát trong không khí - ảnh hưởng đến di chuyển khi nhảy")]
        public float maSatKhongKhi = 0.1f;

        #endregion

        #region Điều Khiển Nâng Cao

        [Header("Điều Khiển Nâng Cao")]
        [Range(0.1f, 5f)]
        [Tooltip("Tốc độ xoay nhân vật khi thay đổi hướng")]
        public float tocDoXoay = 1f;
        
        [Range(0f, 1f)]
        [Tooltip("Độ mượt khi xoay nhân vật")]
        public float doMuotXoay = 0.1f;
        
        [Tooltip("Cho phép di chuyển trong không khí")]
        public bool choPhepDiChuyenTrongKhongKhi = true;
        
        [Range(0.1f, 1f)]
        [Tooltip("Hệ số điều khiển trong không khí")]
        public float heSoDieuKhienKhongKhi = 0.5f;

        #endregion

        #region Slope và Terrain

        [Header("Dốc và Địa Hình")]
        [Range(0f, 90f)]
        [Tooltip("Góc dốc tối đa mà nhân vật có thể leo (độ)")]
        public float gocDocToiDa = 45f;
        
        [Range(0.1f, 2f)]
        [Tooltip("Hệ số tốc độ khi leo dốc")]
        public float heSoTocDoLeoDoc = 0.8f;
        
        [Range(0.1f, 2f)]
        [Tooltip("Hệ số tốc độ khi xuống dốc")]
        public float heSoTocDoXuongDoc = 1.2f;
        
        [Tooltip("Tự động điều chỉnh tốc độ theo độ dốc")]
        public bool tuDongDieuChinhTocDoTheoDoDoc = true;

        #endregion

        #region Validation và Utility

        /// <summary>
        /// Validate các giá trị cài đặt
        /// </summary>
        /// <returns>True nếu tất cả giá trị hợp lệ</returns>
        public bool Validate()
        {
            bool isValid = true;

            // Kiểm tra tốc độ
            if (tocDoDiBo <= 0)
            {
                Debug.LogWarning("[CaiDatDiChuyen] Tốc độ đi bộ phải lớn hơn 0");
                isValid = false;
            }

            if (tocDoChay <= tocDoDiBo)
            {
                Debug.LogWarning("[CaiDatDiChuyen] Tốc độ chạy phải lớn hơn tốc độ đi bộ");
                isValid = false;
            }

            if (heSoTocDoKhiCui <= 0 || heSoTocDoKhiCui > 1)
            {
                Debug.LogWarning("[CaiDatDiChuyen] Hệ số tốc độ khi cúi phải trong khoảng (0, 1]");
                isValid = false;
            }

            // Kiểm tra thời gian gia tốc
            if (thoiGianTangToc <= 0)
            {
                Debug.LogWarning("[CaiDatDiChuyen] Thời gian tăng tốc phải lớn hơn 0");
                isValid = false;
            }

            if (thoiGianGiamToc <= 0)
            {
                Debug.LogWarning("[CaiDatDiChuyen] Thời gian giảm tốc phải lớn hơn 0");
                isValid = false;
            }

            // Kiểm tra góc dốc
            if (gocDocToiDa < 0 || gocDocToiDa > 90)
            {
                Debug.LogWarning("[CaiDatDiChuyen] Góc dốc tối đa phải trong khoảng [0, 90] độ");
                isValid = false;
            }

            return isValid;
        }

        /// <summary>
        /// Tạo bản sao của cài đặt hiện tại
        /// </summary>
        /// <returns>Bản sao của cài đặt</returns>
        public CaiDatDiChuyen Clone()
        {
            return new CaiDatDiChuyen
            {
                // Tốc độ
                tocDoDiBo = this.tocDoDiBo,
                tocDoChay = this.tocDoChay,
                heSoTocDoKhiCui = this.heSoTocDoKhiCui,
                nguongInputToiThieu = this.nguongInputToiThieu,
                
                // Gia tốc
                thoiGianTangToc = this.thoiGianTangToc,
                thoiGianGiamToc = this.thoiGianGiamToc,
                heSoGiaToc = this.heSoGiaToc,
                heSoGiamToc = this.heSoGiamToc,
                
                // Ma sát
                maSatMatDat = this.maSatMatDat,
                maSatTuong = this.maSatTuong,
                maSatKhongKhi = this.maSatKhongKhi,
                
                // Điều khiển nâng cao
                tocDoXoay = this.tocDoXoay,
                doMuotXoay = this.doMuotXoay,
                choPhepDiChuyenTrongKhongKhi = this.choPhepDiChuyenTrongKhongKhi,
                heSoDieuKhienKhongKhi = this.heSoDieuKhienKhongKhi,
                
                // Slope và terrain
                gocDocToiDa = this.gocDocToiDa,
                heSoTocDoLeoDoc = this.heSoTocDoLeoDoc,
                heSoTocDoXuongDoc = this.heSoTocDoXuongDoc,
                tuDongDieuChinhTocDoTheoDoDoc = this.tuDongDieuChinhTocDoTheoDoDoc
            };
        }

        /// <summary>
        /// Reset về giá trị mặc định
        /// </summary>
        public void ResetToDefaults()
        {
            // Tốc độ
            tocDoDiBo = 5f;
            tocDoChay = 10f;
            heSoTocDoKhiCui = 0.5f;
            nguongInputToiThieu = 0.01f;
            
            // Gia tốc
            thoiGianTangToc = 0.2f;
            thoiGianGiamToc = 0.1f;
            heSoGiaToc = 1f;
            heSoGiamToc = 1f;
            
            // Ma sát
            maSatMatDat = 0.3f;
            maSatTuong = 0.8f;
            maSatKhongKhi = 0.1f;
            
            // Điều khiển nâng cao
            tocDoXoay = 1f;
            doMuotXoay = 0.1f;
            choPhepDiChuyenTrongKhongKhi = true;
            heSoDieuKhienKhongKhi = 0.5f;
            
            // Slope và terrain
            gocDocToiDa = 45f;
            heSoTocDoLeoDoc = 0.8f;
            heSoTocDoXuongDoc = 1.2f;
            tuDongDieuChinhTocDoTheoDoDoc = true;
        }

        /// <summary>
        /// Lấy thông tin tóm tắt về cài đặt
        /// </summary>
        /// <returns>String mô tả cài đặt</returns>
        public string GetSummary()
        {
            return $"Tốc độ đi bộ: {tocDoDiBo}m/s, Tốc độ chạy: {tocDoChay}m/s, " +
                   $"Tăng tốc: {thoiGianTangToc}s, Giảm tốc: {thoiGianGiamToc}s, " +
                   $"Góc dốc tối đa: {gocDocToiDa}°";
        }

        #endregion
    }
}
