using UnityEngine;
using UnityEditor;
using BorderCharacterSystem.Configuration;
using BorderCharacterSystem.Configuration.ScriptableObjects;

namespace BorderCharacterSystem.Editor
{
    /// <summary>
    /// Editor utility để tạo BorderCharacterConfig assets dễ dàng
    /// </summary>
    public static class BorderCharacterConfigCreator
    {
        private const string CONFIG_PATH = "Assets/BorderCharacterSystem/Configuration/ScriptableObjects/";
        
        [MenuItem("Border Character System/Tạo Config/Cấu Hình Mặc Định", priority = 1)]
        public static void CreateDefaultConfig()
        {
            var config = DefaultBorderCharacterConfig.CreateDefault();
            CreateConfigAsset(config, "DefaultConfig");
        }
        
        [MenuItem("Border Character System/Tạo Config/Cấu Hình Documentary Game", priority = 2)]
        public static void CreateDocumentaryConfig()
        {
            var config = DefaultBorderCharacterConfig.CreateDocumentaryConfig();
            CreateConfigAsset(config, "DocumentaryConfig");
        }
        
        [MenuItem("Border Character System/Tạo Config/Cấu Hình Action Game", priority = 3)]
        public static void CreateActionConfig()
        {
            var config = DefaultBorderCharacterConfig.CreateActionConfig();
            CreateConfigAsset(config, "ActionConfig");
        }
        
        [MenuItem("Border Character System/Tạo Config/Cấu Hình Platformer Game", priority = 4)]
        public static void CreatePlatformerConfig()
        {
            var config = DefaultBorderCharacterConfig.CreatePlatformerConfig();
            CreateConfigAsset(config, "PlatformerConfig");
        }
        
        [MenuItem("Border Character System/Tạo Config/Cấu Hình Tùy Chỉnh", priority = 10)]
        public static void CreateCustomConfig()
        {
            var config = ScriptableObject.CreateInstance<BorderCharacterConfig>();
            CreateConfigAsset(config, "CustomConfig");
        }
        
        [MenuItem("Border Character System/Utilities/Validate Tất Cả Configs", priority = 20)]
        public static void ValidateAllConfigs()
        {
            string[] guids = AssetDatabase.FindAssets("t:BorderCharacterConfig");
            int validCount = 0;
            int invalidCount = 0;
            
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                var config = AssetDatabase.LoadAssetAtPath<BorderCharacterConfig>(path);
                
                if (config != null)
                {
                    Debug.Log($"Đang validate config: {config.ConfigName} tại {path}");
                    config.ValidateConfiguration();
                    
                    // Đơn giản hóa validation check
                    if (config.movementSettings.tocDoDiBo > 0)
                    {
                        validCount++;
                        Debug.Log($"✓ Config '{config.ConfigName}' hợp lệ");
                    }
                    else
                    {
                        invalidCount++;
                        Debug.LogWarning($"✗ Config '{config.ConfigName}' có vấn đề");
                    }
                }
            }
            
            Debug.Log($"Validation hoàn tất: {validCount} configs hợp lệ, {invalidCount} configs có vấn đề");
        }
        
        [MenuItem("Border Character System/Utilities/Tạo Tất Cả Configs Mẫu", priority = 21)]
        public static void CreateAllSampleConfigs()
        {
            CreateDefaultConfig();
            CreateDocumentaryConfig();
            CreateActionConfig();
            CreatePlatformerConfig();
            
            Debug.Log("Đã tạo tất cả configs mẫu!");
        }
        
        [MenuItem("Border Character System/Documentation/Mở README", priority = 30)]
        public static void OpenReadme()
        {
            string readmePath = "Assets/BorderCharacterSystem/README.md";
            var readme = AssetDatabase.LoadAssetAtPath<TextAsset>(readmePath);
            
            if (readme != null)
            {
                AssetDatabase.OpenAsset(readme);
            }
            else
            {
                Debug.LogWarning("Không tìm thấy README.md");
            }
        }
        
        [MenuItem("Border Character System/Documentation/Mở API Guide", priority = 31)]
        public static void OpenAPIGuide()
        {
            string apiGuidePath = "API_DESIGN_GUIDE.md";
            var apiGuide = AssetDatabase.LoadAssetAtPath<TextAsset>(apiGuidePath);
            
            if (apiGuide != null)
            {
                AssetDatabase.OpenAsset(apiGuide);
            }
            else
            {
                Debug.LogWarning("Không tìm thấy API_DESIGN_GUIDE.md");
            }
        }
        
        private static void CreateConfigAsset(BorderCharacterConfig config, string defaultName)
        {
            // Ensure directory exists
            if (!AssetDatabase.IsValidFolder(CONFIG_PATH.TrimEnd('/')))
            {
                System.IO.Directory.CreateDirectory(CONFIG_PATH);
                AssetDatabase.Refresh();
            }
            
            // Generate unique filename
            string fileName = defaultName;
            string fullPath = CONFIG_PATH + fileName + ".asset";
            int counter = 1;
            
            while (AssetDatabase.LoadAssetAtPath<BorderCharacterConfig>(fullPath) != null)
            {
                fileName = $"{defaultName}_{counter}";
                fullPath = CONFIG_PATH + fileName + ".asset";
                counter++;
            }
            
            // Create asset
            AssetDatabase.CreateAsset(config, fullPath);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            
            // Select and ping the created asset
            EditorUtility.FocusProjectWindow();
            Selection.activeObject = config;
            EditorGUIUtility.PingObject(config);
            
            Debug.Log($"Đã tạo BorderCharacterConfig: {fileName} tại {fullPath}");
        }
    }
    
    /// <summary>
    /// Custom Inspector cho BorderCharacterConfig
    /// </summary>
    [CustomEditor(typeof(BorderCharacterConfig))]
    public class BorderCharacterConfigEditor : UnityEditor.Editor
    {
        public override void OnInspectorGUI()
        {
            var config = (BorderCharacterConfig)target;
            
            // Header
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Border Character System Config", EditorStyles.boldLabel);
            EditorGUILayout.LabelField($"Phiên bản: {config.Version}", EditorStyles.miniLabel);
            EditorGUILayout.Space();
            
            // Validation button
            if (GUILayout.Button("Validate Cấu Hình", GUILayout.Height(30)))
            {
                config.ValidateConfiguration();
            }
            
            EditorGUILayout.Space();
            
            // Clone button
            if (GUILayout.Button("Tạo Bản Sao", GUILayout.Height(25)))
            {
                var clone = config.Clone();
                BorderCharacterConfigCreator.CreateConfigAsset(clone, config.ConfigName + "_Copy");
            }
            
            // Reset button
            if (GUILayout.Button("Reset Về Mặc Định", GUILayout.Height(25)))
            {
                if (EditorUtility.DisplayDialog("Xác Nhận Reset", 
                    "Bạn có chắc muốn reset tất cả cài đặt về giá trị mặc định?", 
                    "Reset", "Hủy"))
                {
                    config.ResetToDefaults();
                    EditorUtility.SetDirty(config);
                }
            }
            
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("", GUI.skin.horizontalSlider);
            EditorGUILayout.Space();
            
            // Default inspector
            DrawDefaultInspector();
            
            // Footer info
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("", GUI.skin.horizontalSlider);
            EditorGUILayout.LabelField("Border Character System v1.0.0", EditorStyles.centeredGreyMiniLabel);
            
            // Auto-save when changed
            if (GUI.changed)
            {
                EditorUtility.SetDirty(config);
            }
        }
        
        private static void CreateConfigAsset(BorderCharacterConfig config, string defaultName)
        {
            BorderCharacterConfigCreator.CreateConfigAsset(config, defaultName);
        }
    }
}
